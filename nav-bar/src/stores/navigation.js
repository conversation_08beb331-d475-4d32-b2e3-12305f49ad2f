import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getMenuList, getCollectList, getOfficeList, getOfficeCollectList, getBackupList, getUserMenuHomeList, getUserOfficeList } from '@/api/navbar'
import { useUrlStore } from '../stores/url'
import { getFolderList } from '@/api/folder'
import autoBackupManager from '@/utils/autoBackupManager'

export const useNavigationStore = defineStore('navigation', () => {
  // 状态
  const allNavItems = ref([])
  const currentCategory = ref('')
  const categoryApps = ref({})
  const isLoading = ref(false)
  // 添加一个响应式的收藏数据状态
  const collectAppsData = ref([])
  // 添加全局应用列表状态
  const allApps = ref([])
  // 添加特殊卡片状态
  const specialCardsMaster = ref([])
  // 添加当前数据源状态
  const currentDataSource = ref('entertainment')
  // 新增：启动模式设置 ('last', 'entertainment', 'office', 'pure')
  const startupMode = ref(localStorage.getItem('startupMode') || 'last')
  // 添加数据源缓存
  const dataSourceCache = ref({
    entertainment: null,
    office: null
  })
  const urlStore = useUrlStore();
  // 计算属性
  const categories = computed(() => {
    const menuCategories = allNavItems.value.map(item => ({
      type: item.type,
      icon: item.ico,
      name: item.type,
      id: item.id
    }))

    // 添加收藏分类
    const collectCategory = {
      type: 'collectNav',
      icon: 'https://file.yeslinks.cn/ico/menu-3.svg', // 你可以自定义收藏图标
      name: '收藏',
      id: 'unset'
    }

    return [...menuCategories, collectCategory]
  })


  const currentApps = computed(() => {
    if (currentCategory.value === 'collectNav') {
      // 使用响应式的收藏数据
      return collectAppsData.value
    }
    const regularData = categoryApps.value[currentCategory.value] || []
    return regularData
  })

  const currentCategoryData = computed(() =>
    allNavItems.value.find(item => item.type === currentCategory.value)
  )

  // 方法
  // 检查用户是否已登录
  function isUserLoggedIn() {
    const token = localStorage.getItem('token')
    return !!token
  }

  // 为登录用户加载备份数据
  async function loadBackupDataForLoggedInUser() {
    try {
      const response = await getBackupList()

      if (response.status === 200 && response.data && response.data.length > 0) {
        const firstBackup = response.data[0]

        // 解析备份数据
        const backupData = typeof firstBackup.data === 'string'
          ? JSON.parse(firstBackup.data)
          : firstBackup.data

        return backupData
      } else {
        console.warn('🔍 Navigation Store - API返回数据异常或无备份数据:', response)
        return null
      }
    } catch (error) {
      console.error('🔍 Navigation Store - 获取备份数据失败:', error)
      return null
    }
  }

  // 从备份数据加载收藏数据
  async function loadCollectAppsDataFromBackup() {
    try {
      const backupData = await loadBackupDataForLoggedInUser()
      if (!backupData) {
        await loadCollectAppsData()
        return
      }

      const collectKey = getCollectStorageKey() // homeDockApps 或 officeDockApps
      if (backupData[collectKey]) {
        try {
          const collectData = typeof backupData[collectKey] === 'string'
            ? JSON.parse(backupData[collectKey])
            : backupData[collectKey]

          collectAppsData.value = Array.isArray(collectData) ? collectData : []
        } catch (error) {
          console.error(`🔍 Navigation Store - 解析备份收藏数据失败 ${collectKey}:`, error)
          collectAppsData.value = []
        }
      } else {
        console.warn(`🔍 Navigation Store - 备份数据中未找到收藏数据 ${collectKey}`)
        collectAppsData.value = []
      }
    } catch (error) {
      console.error('🔍 Navigation Store - 从备份加载收藏数据失败:', error)
      collectAppsData.value = []
    }
  }

  async function initNavigation() {
    if (allNavItems.value.length > 0) return // 避免重复初始化

    isLoading.value = true
    try {
      // 恢复数据源状态 - 必须先执行这一步
      const savedDataSource = localStorage.getItem('currentDataSource')
      if (savedDataSource && (savedDataSource === 'entertainment' || savedDataSource === 'office')) {
        currentDataSource.value = savedDataSource
      }

      // 预加载两个数据源的API数据到缓存
      await preloadAllDataSources()

      // 根据当前数据源获取对应的API数据 - 确保使用正确的数据源
      let apiData = dataSourceCache.value[currentDataSource.value]
      if (!apiData) {
        console.warn(`Navigation Store: 缓存中没有${currentDataSource.value}数据，重新获取`)
        // 如果缓存中没有，重新获取
        let apiResponse
        switch (currentDataSource.value) {
          case 'entertainment':
            apiResponse = await getMenuList()
            break
          case 'office':
            apiResponse = await getOfficeList()
            break
          default:
            apiResponse = await getMenuList()
            currentDataSource.value = 'entertainment'
        }
        if (apiResponse && apiResponse.status === 200) {
          apiData = apiResponse
          // urlStore.setAppUrl(menuRes.logo);
          dataSourceCache.value[currentDataSource.value] = apiData
        }
      }

      // 登录用户优先使用API备份数据，未登录用户使用localStorage
      let hasLocalData = false
      let useBackupData = false

      if (isUserLoggedIn()) {
        const backupData = await loadBackupDataForLoggedInUser()

        if (backupData) {
          // 应用备份数据到categoryApps
          const backupKeys = [
            // 'categoryApps_entertainment', 'categoryApps_office'
            'categoryApps_entertainment', 
            'homeDockApps', 
            'categoryApps_office', 
            'officeDockApps',
          ]
          backupKeys.forEach(key => {
            if (backupData[key]) {
              try {
                const parsedData = typeof backupData[key] === 'string'
                  ? JSON.parse(backupData[key])
                  : backupData[key]

                if (key === `categoryApps_${currentDataSource.value}`) {
                  // 清空现有数据
                  Object.keys(categoryApps.value).forEach(k => {
                    delete categoryApps.value[k]
                  })
                  // 应用备份数据
                  Object.assign(categoryApps.value, parsedData)
                }
              } catch (error) {
                console.error(`🔍 Navigation Store - 解析备份数据失败 ${key}:`, error)
              }
            }
          })

          useBackupData = true
          hasLocalData = Object.keys(categoryApps.value).length > 0
        } else {
          hasLocalData = loadAllCategoryData(currentDataSource.value)
        }
      } else {
        hasLocalData = loadAllCategoryData(currentDataSource.value)
      }
      // 加载收藏数据 - 确保对应当前数据源
      if (useBackupData && isUserLoggedIn()) {
        // 如果使用了备份数据，需要特殊处理收藏数据
        await loadCollectAppsDataFromBackup()
      } else {
        await loadCollectAppsData()
      }

      if (apiData && apiData.data) {
        allNavItems.value = apiData.data
        // 如果没有本地数据，则初始化API数据
        if (!hasLocalData) {
          initCategoryData(apiData.data)
        } else {
          // 如果有本地数据，需要将categoryApps的数据同步到allApps
          allApps.value = []
          Object.values(categoryApps.value).forEach(categoryAppsList => {
            if (Array.isArray(categoryAppsList)) {
              allApps.value.push(...categoryAppsList)
            }
          })
        }

        // 设置默认分类
        if (apiData.data.length > 0) {
          // 如果当前分类为空，或者当前分类在新数据中不存在，切换到第一个分类
          const categoryExists = currentCategory.value && apiData.data.some(item => item.type === currentCategory.value)
          if (!currentCategory.value || !categoryExists) {
            currentCategory.value = apiData.data[0].type
          }

          // 如果已有本地数据，不需要重新加载
          if (!hasLocalData) {
            loadCategoryApps(currentCategory.value)
          }
        }
      }

    } catch (error) {
      console.error('初始化导航失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  // 预加载所有数据源的API数据
  async function preloadAllDataSources() {
    try {
      // 保存当前数据源状态
      const originalDataSource = currentDataSource.value

      // 并行加载两个数据源的API数据
      const [entertainmentResponse, officeResponse] = await Promise.allSettled([
        getMenuList(),
        getOfficeList()
      ])

      // 处理娱乐模式数据
      if (entertainmentResponse.status === 'fulfilled' && entertainmentResponse.value?.status === 200) {
        urlStore.setAppUrl(entertainmentResponse.value.logo);
        dataSourceCache.value.entertainment = entertainmentResponse.value

        // 初始化并保存娱乐模式数据到 localStorage
        await initAndSaveDataSource('entertainment', entertainmentResponse.value)
      } else {
        console.warn('Navigation Store: 娱乐模式数据预加载失败:', entertainmentResponse.reason)
      }

      // 处理办公模式数据
      if (officeResponse.status === 'fulfilled' && officeResponse.value?.status === 200) {
        urlStore.setAppUrl(entertainmentResponse.value.logo);
        dataSourceCache.value.office = officeResponse.value

        // 初始化并保存办公模式数据到 localStorage
        await initAndSaveDataSource('office', officeResponse.value)
      } else {
        console.warn('Navigation Store: 办公模式数据预加载失败:', officeResponse.reason)
      }

      // 恢复原始数据源状态
      currentDataSource.value = originalDataSource

    } catch (error) {
      console.error('Navigation Store: 预加载数据源失败:', error)
    }
  }

  // 初始化并保存单个数据源到 localStorage
  async function initAndSaveDataSource(dataSource, apiData) {
    try {

      // 检查是否已有该数据源的本地数据
      const storageKey = `categoryApps_${dataSource}`
      const existingData = localStorage.getItem(storageKey)

      if (existingData) {
        return
      }

      // 临时切换数据源以确保保存到正确的 localStorage 键
      const originalDataSource = currentDataSource.value
      currentDataSource.value = dataSource

      // 临时保存当前的 categoryApps 状态
      const originalCategoryApps = { ...categoryApps.value }

      // 清空 categoryApps 准备处理新数据源
      Object.keys(categoryApps.value).forEach(key => {
        delete categoryApps.value[key]
      })

      // 处理 API 数据并初始化分类
      if (apiData && apiData.data && apiData.data.length > 0) {
        apiData.data.forEach(categoryData => {
          const categoryType = categoryData.type
          if (categoryData.children) {
            const processedApps = categoryData.children.map(child => {
              // 处理类型转换：collect -> collection
              let appType = child.webType || 'app'
              if (appType === 'collect') {
                appType = 'collection'
              }

              // 处理文件夹和合集
              if (child.webType === 'folder' || child.webType === 'collection' || child.webType === 'collect') {
                // 处理子项
                const folderChildren = Array.isArray(child.children) ?
                  child.children.map(subChild => ({
                    id: subChild.id,
                    name: subChild.name,
                    icon: subChild.logo,
                    color: subChild.color || '#ffffff',
                    category: categoryType,
                    size: { w: parseInt(subChild.w || 1), h: parseInt(subChild.h || 1) },
                    url: subChild.websiteAddress ? `https://${subChild.websiteAddress}` : '',
                    type: subChild.webType || 'app',
                    description: subChild.descs || '',
                    x: parseInt(subChild.x || 0),
                    y: parseInt(subChild.y || 0),
                    websiteAddress: subChild.websiteAddress,
                    iscanopen: subChild.iscanopen,
                    isfixed: subChild.isfixed
                  })) : []

                return {
                  id: (child.webType === 'collection' || child.webType === 'collect')
                    ? `collection-${child.id || `${child.name}-${Math.round(Math.random() * 10000)}`}`
                    : `folder-${child.id || `${child.name}-${Math.round(Math.random() * 10000)}`}`,
                  name: child.type || child.name || ((child.webType === 'collection' || child.webType === 'collect') ? '应用合集' : '文件夹'),
                  icon: child.logo || child.icon || ((child.webType === 'collection' || child.webType === 'collect') ? '📦' : '📁'),
                  color: child.color || '#ffffff',
                  category: categoryType,
                  size: { w: parseInt(child.w || 2), h: parseInt(child.h || 2) },
                  type: appType,
                  children: folderChildren,
                  description: `包含${folderChildren.length}个应用`,
                  originalId: child.id,
                  x: parseInt(child.x || 0),
                  y: parseInt(child.y || 0),
                  websiteAddress: child.websiteAddress,
                  iscanopen: child.iscanopen,
                  isfixed: child.isfixed
                }
              } else {
                // 处理普通应用
                return {
                  id: child.id,
                  name: child.name,
                  icon: child.logo,
                  color: child.color || '#ffffff',
                  category: categoryType,
                  size: { w: parseInt(child.w || 1), h: parseInt(child.h || 1) },
                  url: child.websiteAddress ? `https://${child.websiteAddress}` : '',
                  type: appType,
                  description: child.descs || '',
                  x: parseInt(child.x || 0),
                  y: parseInt(child.y || 0),
                  websiteAddress: child.websiteAddress,
                  iscanopen: child.iscanopen,
                  isfixed: child.isfixed
                }
              }
            })

            // 设置分类应用
            categoryApps.value[categoryType] = processedApps
          }
        })

        // 保存到 localStorage
        saveAllCategoryData()
      }

      // 恢复原始状态
      currentDataSource.value = originalDataSource
      Object.keys(categoryApps.value).forEach(key => {
        delete categoryApps.value[key]
      })
      Object.assign(categoryApps.value, originalCategoryApps)

    } catch (error) {
      console.error(`Navigation Store: 初始化并保存 ${dataSource} 数据源失败:`, error)
    }
  }

  // 加载用户数据源的API数据
  async function loadUserDataSources() {
    try {

      // 并行加载两个用户数据源的API数据
      const [userEntertainmentResponse, userOfficeResponse] = await Promise.allSettled([
        getUserMenuHomeList(),
        getUserOfficeList()
      ])

      const userEntertainmentMergedMap = new Map();
      const userOfficeMergedMap = new Map();
      const userEntertainmentData = userEntertainmentResponse.value.data
      const userOfficeData = userOfficeResponse.value.data
      for (const item of userEntertainmentData) {
        if (userEntertainmentMergedMap.has(item.id)) {
          const existing = userEntertainmentMergedMap.get(item.id);
          existing.children = existing.children.concat(item.children);
        } else {
          // 创建副本，避免修改原数组元素
          userEntertainmentMergedMap.set(item.id, { ...item, children: [...item.children] });
        }
      }

      for (const item of userOfficeData) {
        if (userOfficeMergedMap.has(item.id)) {
          const existing = userOfficeMergedMap.get(item.id);
          existing.children = existing.children.concat(item.children);
        } else {
          // 创建副本，避免修改原数组元素
          userOfficeMergedMap.set(item.id, { ...item, children: [...item.children] });
        }
      }
      userEntertainmentResponse.value.data  = Array.from(userEntertainmentMergedMap.values());
      userOfficeResponse.value.data  = Array.from(userOfficeMergedMap.values());
      
      // 处理用户娱乐模式数据
      if (userEntertainmentResponse.status === 'fulfilled' && userEntertainmentResponse.value?.status === 200) {
        urlStore.setAppUrl(userEntertainmentResponse.value.logo);
        dataSourceCache.value.entertainment = userEntertainmentResponse.value
      } else {
        console.warn('Navigation Store: 用户娱乐模式数据加载失败:', userEntertainmentResponse.reason)
        throw new Error('用户娱乐模式数据加载失败')
      }

      // 处理用户办公模式数据
      if (userOfficeResponse.status === 'fulfilled' && userOfficeResponse.value?.status === 200) {
        urlStore.setAppUrl(userEntertainmentResponse.value.logo);
        dataSourceCache.value.office = userOfficeResponse.value
      } else {
        console.warn('Navigation Store: 用户办公模式数据加载失败:', userOfficeResponse.reason)
        throw new Error('用户办公模式数据加载失败')
      }

      return true
    } catch (error) {
      console.error('Navigation Store: 加载用户数据源失败:', error)
      throw error
    }
  }

  // 收藏功能直接使用现有的 homeDockApps 系统，不需要重复实现

  // 根据数据源获取收藏数据的localStorage键
  function getCollectStorageKey() {
    return currentDataSource.value === 'office' ? 'officeDockApps' : 'homeDockApps'
  }

  // 加载收藏数据到响应式状态
  async function loadCollectAppsData() {
    try {
      const storageKey = getCollectStorageKey()
      const savedDockApps = localStorage.getItem(storageKey)
      if (savedDockApps) {
        collectAppsData.value = JSON.parse(savedDockApps)
      } else {
        // 如果localStorage中没有数据，从API初始化
        await initCollectAppsFromAPI()
      }
    } catch (error) {
      console.warn('加载收藏数据失败:', error)
      collectAppsData.value = []
    }
  }

  // 从API初始化收藏数据
  async function initCollectAppsFromAPI() {
    try {
      let apiResponse
      switch (currentDataSource.value) {
        case 'entertainment':
          apiResponse = await getCollectList()
          break
        case 'office':
          apiResponse = await getOfficeCollectList()
          break
        default:
          console.warn(`未知的数据源: ${currentDataSource.value}`)
          return
      }

      if (apiResponse && apiResponse.status === 200 && Array.isArray(apiResponse.data)) {
        collectAppsData.value = apiResponse.data.map(item => ({
          id: item.id,
          name: item.name,
          icon: item.logo,
          color: item.color,
          description: item.descs,
          url: item.websiteAddress,
          websiteAddress: item.websiteAddress,
          iscanopen: item.iscanopen,
          isfixed: item.isfixed  // 0固定 1取消固定
        }))

        // 保存到localStorage
        const storageKey = getCollectStorageKey()
        localStorage.setItem(storageKey, JSON.stringify(collectAppsData.value))

        // 触发localStorage变化事件，通知Home.vue更新dock栏数据
        window.dispatchEvent(new CustomEvent('dockAppsUpdated', {
          detail: {
            dataSource: currentDataSource.value,
            storageKey: storageKey,
            data: collectAppsData.value
          }
        }))
      } else {
        console.warn(`Navigation Store: API返回数据异常:`, apiResponse)
        collectAppsData.value = []
      }
    } catch (error) {
      console.error(`Navigation Store: 从API初始化收藏数据失败:`, error)
      collectAppsData.value = []
    }
  }

  // 刷新收藏数据（当 localStorage 发生变化时调用）
  async function refreshCollectAppsData() {
    await loadCollectAppsData()
  }

  async function switchCategory(categoryType) {
    if (categoryType === currentCategory.value) return

    // 保存当前分类数据
    saveCategoryData()

    // 切换分类
    currentCategory.value = categoryType
    
    // 如果不是收藏分类，加载普通分类数据
    if (categoryType !== 'collectNav') {
      loadCategoryApps(categoryType)
    } else {
      await loadCollectAppsData()
    }
    // 收藏分类直接从 localStorage 读取，不需要特殊处理
  }

  function loadCategoryApps(categoryType) {

    // 优先从统一的 categoryApps 存储加载
    if (categoryApps.value[categoryType] && categoryApps.value[categoryType].length > 0) {
      return
    }

    // 尝试从当前数据源的 localStorage 加载
    const storageKey = `categoryApps_${currentDataSource.value}`
    const unifiedData = localStorage.getItem(storageKey)
    if (unifiedData) {
      try {
        const parsedUnifiedData = JSON.parse(unifiedData)
        if (parsedUnifiedData[categoryType]) {
          // 处理类型转换：collect -> collection
          parsedUnifiedData[categoryType].forEach(app => {
            if (app.type === 'collect') {
              app.type = 'collection'
            }
          })
          categoryApps.value[categoryType] = parsedUnifiedData[categoryType]
          return
        }
      } catch (error) {
        console.warn('加载统一存储数据失败:', error)
      }
    }

    // 兼容旧的分散存储（迁移用）
    const oldSavedData = localStorage.getItem(`layout_${categoryType}`)
    if (oldSavedData) {
      try {
        const parsedData = JSON.parse(oldSavedData)
        // 处理类型转换：collect -> collection
        parsedData.forEach(app => {
          if (app.type === 'collect') {
            app.type = 'collection'
          }
        })
        categoryApps.value[categoryType] = parsedData

        // 迁移到新的统一存储并删除旧数据
        saveAllCategoryData()
        localStorage.removeItem(`layout_${categoryType}`)
        return
      } catch (error) {
        console.warn('加载旧存储数据失败:', error)
      }
    }
    
    // 从API数据转换
    const categoryData = allNavItems.value.find(item => item.type === categoryType)
    if (categoryData?.children) {
      categoryApps.value[categoryType] = categoryData.children
        .map(child => {
          // 处理类型转换：collect -> collection
          let appType = child.webType || 'app'
          if (appType === 'collect') {
            appType = 'collection'
          }

          return {
            id: child.id,
            name: child.name || child.type,
            icon: child.logo,
            color: child.color || '#ffffff',
            category: categoryType,
            size: { w: parseInt(child.w || 1), h: parseInt(child.h || 1) },
            url: child.websiteAddress ? `https://${child.websiteAddress}` : '',
            type: appType,
            description: child.descs || '',
            x: parseInt(child.x),
            y: parseInt(child.y),
            websiteAddress: child.websiteAddress,
            iscanopen: child.iscanopen,
            isfixed: child.isfixed,
            // 保留 originalId 用于合集
            ...(child.webType === 'collect' && { originalId: child.id })
          }
        })
    }
  }

  function saveCategoryData() {
    // 收藏分类由现有的 homeDockApps 系统管理，不需要在这里处理
    if (currentCategory.value !== 'collectNav' && currentApps.value.length > 0) {
      localStorage.setItem(
        `layout_${currentCategory.value}`,
        JSON.stringify(currentApps.value)
      )
    }
    // 同时保存所有分类数据
    saveAllCategoryData()
  }

  function addApp(app, categoryType = currentCategory.value) {
    // 收藏分类由现有的 addToDock 函数处理，这里只处理普通分类
    if (categoryType !== 'collectNav') {
      if (!categoryApps.value[categoryType]) {
        categoryApps.value[categoryType] = []
      }

      // 检查是否存在相同ID的应用
      const existingApp = categoryApps.value[categoryType].find(existingApp => existingApp.id === app.id)

      if (existingApp) {
        // 如果是相同类型的应用，不允许重复添加
        if (existingApp.type === app.type) {
          console.warn(`Navigation Store: 分类 ${categoryType} 中已存在相同ID和类型的应用:`, {
            existingApp: { id: existingApp.id, name: existingApp.name, type: existingApp.type },
            newApp: { id: app.id, name: app.name, type: app.type }
          })
          return false // 返回false表示添加失败
        }

        // 文件夹和普通应用可以共存，记录警告
        console.warn(`Navigation Store: 分类 ${categoryType} 中存在相同ID但不同类型的应用，允许共存:`, {
          existingApp: { id: existingApp.id, name: existingApp.name, type: existingApp.type },
          newApp: { id: app.id, name: app.name, type: app.type }
        })
      }

      categoryApps.value[categoryType].push(app)

      // 同时添加到全局应用列表，避免重复
      const existingAppIndex = allApps.value.findIndex(existingApp => existingApp.id === app.id)
      if (existingAppIndex === -1) {
        allApps.value.push(app)
      }

      saveCategoryData()
      return true // 返回true表示添加成功
    }
    return false
  }

  function removeApp(appId, categoryType = currentCategory.value) {
    // 收藏分类由现有的 removeFromDock 函数处理，这里只处理普通分类
    if (categoryType !== 'collectNav' && categoryApps.value[categoryType]) {
      categoryApps.value[categoryType] = categoryApps.value[categoryType]
        .filter(app => app.id !== appId)
      saveCategoryData()
    }
  }

  function updateAppOrder(apps, categoryType = currentCategory.value) {
    // 收藏分类由现有的 DockBar 拖拽系统处理，这里只处理普通分类
    if (categoryType !== 'collectNav') {
      categoryApps.value[categoryType] = apps
      saveCategoryData()
    }
  }

  // 新增：设置全局应用列表
  function setAllApps(apps) {
    allApps.value = apps
  }

  // 新增：设置特殊卡片
  function setSpecialCards(cards) {
    specialCardsMaster.value = cards
  }

  // 新增：更新数据源和相关分类数据
  async function updateDataSource(dataSource) {
    if (currentDataSource.value === dataSource) return


    try {
      // 保存当前数据源的分类数据
      saveAllCategoryData()

      let apiData = null

      // 检查缓存
      if (dataSourceCache.value[dataSource]) {
        apiData = dataSourceCache.value[dataSource]
      } else {
        // 如果缓存中没有，说明预加载可能失败了，重新获取
        console.warn(`Navigation Store: 缓存中没有${dataSource}数据，重新获取`)
        let apiResponse
        switch (dataSource) {
          case 'entertainment':
            apiResponse = await getMenuList()
            break
          case 'office':
            apiResponse = await getOfficeList()
            break
          default:
            console.warn(`未知的数据源: ${dataSource}`)
            return
        }

        if (apiResponse && apiResponse.status === 200) {
          apiData = apiResponse
          // 缓存数据
          dataSourceCache.value[dataSource] = apiData
        } else {
          throw new Error(`${dataSource}数据加载失败`)
        }
      }

      // 更新当前数据源
      currentDataSource.value = dataSource

      // 保存数据源状态到localStorage
      localStorage.setItem('currentDataSource', dataSource)

      // 更新分类数据
      if (apiData && apiData.data) {
        allNavItems.value = apiData.data

        // 尝试加载新数据源的已保存数据
        const hasLocalData = loadAllCategoryData(dataSource)

        // 如果没有本地数据，则从API初始化
        if (!hasLocalData) {
          initCategoryData(apiData.data)
        }

        // 如果当前分类在新数据中不存在，切换到第一个分类
        const categoryExists = apiData.data.some(item => item.type === currentCategory.value)
        if (!categoryExists && apiData.data.length > 0) {
          const firstCategory = apiData.data[0].type
          switchCategory(firstCategory)
        }

        // 重新加载收藏数据（因为数据源已切换）
        await loadCollectAppsData()
      }

    } catch (error) {
      console.error('Navigation Store: 数据源切换失败:', error)
      throw error
    }
  }

  // 新增：设置数据源
  function setDataSource(dataSource) {
    // 调用updateDataSource来处理数据源切换
    return updateDataSource(dataSource)
  }

  // 新增：获取应用通过ID
  function getAppById(appId) {
    // 先从当前分类查找
    const currentCategoryApps = categoryApps.value[currentCategory.value] || []
    let app = currentCategoryApps.find(a => a.id === appId)

    if (!app) {
      // 从所有分类中查找
      for (const categoryType in categoryApps.value) {
        const apps = categoryApps.value[categoryType] || []
        app = apps.find(a => a.id === appId)
        if (app) break
      }
    }

    if (!app) {
      // 从全局应用列表查找
      app = allApps.value.find(a => a.id === appId)
    }

    return app
  }

  // 新增：更新应用属性
  function updateApp(appId, updates, categoryType = currentCategory.value) {
    if (categoryType === 'collectNav') {
      // 收藏分类的更新由现有系统处理
      return
    }

    const apps = categoryApps.value[categoryType] || []
    const appIndex = apps.findIndex(app => app.id === appId)

    if (appIndex !== -1) {
      // 更新应用属性
      Object.assign(apps[appIndex], updates)

      // 同时更新全局应用列表
      const globalAppIndex = allApps.value.findIndex(app => app.id === appId)
      if (globalAppIndex !== -1) {
        Object.assign(allApps.value[globalAppIndex], updates)
      }

      saveCategoryData()
    }
  }

  // 新增：移动应用到文件夹
  function moveAppToFolder(appId, folderId, categoryType = currentCategory.value) {
    if (categoryType === 'collectNav') return

    const apps = categoryApps.value[categoryType] || []
    const appIndex = apps.findIndex(a => a.id === appId)
    const folderIndex = apps.findIndex(a => a.id === folderId)

    if (appIndex !== -1 && folderIndex !== -1) {
      const app = apps[appIndex]
      const folder = apps[folderIndex]

      // 确保文件夹有children数组
      if (!folder.children) {
        folder.children = []
      }

      // 移动应用到文件夹
      folder.children.push({ ...app })
      apps.splice(appIndex, 1)

      saveCategoryData()
    }
  }

  // 保存所有分类数据到 localStorage
  function saveAllCategoryData() {
    try {
      const storageKey = `categoryApps_${currentDataSource.value}`
      localStorage.setItem(storageKey, JSON.stringify(categoryApps.value))
    } catch (error) {
      console.error(`保存分类数据失败 (数据源: ${currentDataSource.value}):`, error)
    }
  }

  // 从 localStorage 加载所有分类数据
  function loadAllCategoryData(dataSource = currentDataSource.value) {
    try {
      const storageKey = `categoryApps_${dataSource}`
      const savedData = localStorage.getItem(storageKey)
      if (savedData) {
        const parsed = JSON.parse(savedData)
        
        // 清空现有的分类数据，避免混合不同数据源的数据
        Object.keys(categoryApps.value).forEach(key => {
          delete categoryApps.value[key]
        })
        
        // 加载指定数据源的分类数据
        Object.assign(categoryApps.value, parsed)
        
        return true
      }
    } catch (error) {
      console.error(`加载分类数据失败 (数据源: ${dataSource}):`, error)
    }
    return false
  }



  // 新增：初始化分类数据
  function initCategoryData(apiData) {

    // 清空现有数据
    Object.keys(categoryApps.value).forEach(key => {
      delete categoryApps.value[key]
    })

    // 清空allApps，准备重新填充
    allApps.value = []

    // 处理 API 数据
    if (apiData && apiData.length > 0) {
      apiData.forEach(categoryData => {
        const categoryType = categoryData.type
        if (categoryData.children) {
          const processedApps = categoryData.children
            .map(child => {
              // 处理类型转换：collect -> collection
              let appType = child.webType || 'app'
              if (appType === 'collect') {
                appType = 'collection'
              }

              // 处理文件夹和合集
              if (child.webType === 'folder' || child.webType === 'collection' || child.webType === 'collect') {
                // 处理子项
                const folderChildren = Array.isArray(child.children) ?
                  child.children.map(subChild => ({
                    id: subChild.id,
                    name: subChild.name,
                    icon: subChild.logo,
                    color: subChild.color || '#ffffff',
                    category: categoryType,
                    size: { w: parseInt(subChild.w || 1), h: parseInt(subChild.h || 1) },
                    url: subChild.websiteAddress ? `https://${subChild.websiteAddress}` : '',
                    type: subChild.webType || 'app',
                    description: subChild.descs || '',
                    x: parseInt(subChild.x || 0),
                    y: parseInt(subChild.y || 0),
                    websiteAddress: subChild.websiteAddress,
                    iscanopen: subChild.iscanopen,
                    isfixed: subChild.isfixed
                  })) : []

                return {
                  id: (child.webType === 'collection' || child.webType === 'collect')
                    ? `collection-${child.id || `${child.name}-${Math.round(Math.random() * 10000)}`}`
                    : `folder-${child.id || `${child.name}-${Math.round(Math.random() * 10000)}`}`,
                  name: child.type || child.name || ((child.webType === 'collection' || child.webType === 'collect') ? '应用合集' : '文件夹'),
                  icon: child.logo || child.icon || ((child.webType === 'collection' || child.webType === 'collect') ? '📦' : '📁'),
                  color: child.color || '#ffffff',
                  category: categoryType,
                  size: { w: parseInt(child.w || 2), h: parseInt(child.h || 2) },
                  type: appType,
                  children: folderChildren,
                  description: `包含${folderChildren.length}个应用`,
                  originalId: child.id,
                  x: parseInt(child.x || 0),
                  y: parseInt(child.y || 0),
                  websiteAddress: child.websiteAddress,
                  iscanopen: child.iscanopen,
                  isfixed: child.isfixed
                }
              } else {
                // 处理普通应用
                return {
                  id: child.id,
                  name: child.name,
                  icon: child.logo,
                  color: child.color || '#ffffff',
                  category: categoryType,
                  size: { w: parseInt(child.w || 1), h: parseInt(child.h || 1) },
                  url: child.websiteAddress ? `https://${child.websiteAddress}` : '',
                  type: appType,
                  description: child.descs || '',
                  x: parseInt(child.x || 0),
                  y: parseInt(child.y || 0),
                  websiteAddress: child.websiteAddress,
                  iscanopen: child.iscanopen,
                  isfixed: child.isfixed
                }
              }
            })

          // 设置分类应用
          categoryApps.value[categoryType] = processedApps

          // 将所有应用添加到allApps中
          allApps.value.push(...processedApps)

        }
      })
    }
    saveAllCategoryData()
  }

  // 新增：设置启动模式
  function setStartupMode(mode) {
    startupMode.value = mode
    localStorage.setItem('startupMode', mode)
  }

  // 重置为用户数据
  async function resetToUserData() {
    try {
      // 清除现有的本地存储数据
      const storageKeysToRemove = [
        'categoryApps_entertainment',
        'categoryApps_office',
        'homeDockApps',
        'officeDockApps',
        'navBarCategoryLayouts'
      ]

      storageKeysToRemove.forEach(key => {
        localStorage.removeItem(key)
      })

      // 清空内存中的数据
      Object.keys(categoryApps.value).forEach(key => {
        delete categoryApps.value[key]
      })
      allApps.value = []
      collectAppsData.value = []

      // 加载用户数据源
      await loadUserDataSources()

      // 重新初始化分类数据
      const currentApiData = dataSourceCache.value[currentDataSource.value]
      if (currentApiData && currentApiData.data) {
        allNavItems.value = currentApiData.data
        initCategoryData(currentApiData.data)

        // 重新加载收藏数据
        await initCollectAppsFromAPI()

        // 设置默认分类
        if (currentApiData.data.length > 0) {
          const firstCategory = currentApiData.data[0].type
          currentCategory.value = firstCategory
        }
      }
      //
      await autoBackupManager.performBackup()
      return true

    } catch (error) {
      console.error('Navigation Store: 重置为用户数据失败:', error)
      throw error
    }
  }

  // 强制刷新所有API数据并存储到localStorage
  async function refreshAllApiData() {
    try {

      const results = {
        success: false,
        menuList: { success: false, error: null },
        officeList: { success: false, error: null },
        collectList: { success: false, error: null },
        officeCollectList: { success: false, error: null },
        message: ''
      }

      // 保存当前数据源状态
      const originalDataSource = currentDataSource.value

      // 并行调用所有四个API接口
      const [menuResponse, officeResponse, collectResponse, officeCollectResponse] = await Promise.allSettled([
        getMenuList(),
        getOfficeList(),
        getCollectList(),
        getOfficeCollectList()
      ])

      // 处理 getMenuList 结果
      if (menuResponse.status === 'fulfilled' && menuResponse.value?.status === 200) {
        try {
          // 临时切换到娱乐模式以确保正确存储
          currentDataSource.value = 'entertainment'

          // 更新缓存
          dataSourceCache.value.entertainment = menuResponse.value
          urlStore.setAppUrl(menuResponse.value.logo)

          // 强制重新初始化并保存数据（忽略现有数据）
          await forceInitAndSaveDataSource('entertainment', menuResponse.value)

          results.menuList.success = true
        } catch (error) {
          results.menuList.error = error.message
          console.error('❌ Navigation Store - getMenuList 数据处理失败:', error)
        }
      } else {
        results.menuList.error = menuResponse.reason || 'API调用失败'
        console.error('❌ Navigation Store - getMenuList API调用失败:', menuResponse.reason)
      }

      // 处理 getOfficeList 结果
      if (officeResponse.status === 'fulfilled' && officeResponse.value?.status === 200) {
        try {
          // 临时切换到办公模式以确保正确存储
          currentDataSource.value = 'office'

          // 更新缓存
          dataSourceCache.value.office = officeResponse.value

          // 强制重新初始化并保存数据（忽略现有数据）
          await forceInitAndSaveDataSource('office', officeResponse.value)

          results.officeList.success = true
        } catch (error) {
          results.officeList.error = error.message
          console.error('❌ Navigation Store - getOfficeList 数据处理失败:', error)
        }
      } else {
        results.officeList.error = officeResponse.reason || 'API调用失败'
        console.error('❌ Navigation Store - getOfficeList API调用失败:', officeResponse.reason)
      }

      // 处理 getCollectList 结果
      if (collectResponse.status === 'fulfilled' && collectResponse.value?.status === 200) {
        try {
          currentDataSource.value = 'entertainment'
          await forceUpdateCollectData('entertainment', collectResponse.value)
          results.collectList.success = true
        } catch (error) {
          results.collectList.error = error.message
          console.error('❌ Navigation Store - getCollectList 数据处理失败:', error)
        }
      } else {
        results.collectList.error = collectResponse.reason || 'API调用失败'
        console.error('❌ Navigation Store - getCollectList API调用失败:', collectResponse.reason)
      }

      // 处理 getOfficeCollectList 结果
      if (officeCollectResponse.status === 'fulfilled' && officeCollectResponse.value?.status === 200) {
        try {
          currentDataSource.value = 'office'
          await forceUpdateCollectData('office', officeCollectResponse.value)
          results.officeCollectList.success = true
        } catch (error) {
          results.officeCollectList.error = error.message
          console.error('❌ Navigation Store - getOfficeCollectList 数据处理失败:', error)
        }
      } else {
        results.officeCollectList.error = officeCollectResponse.reason || 'API调用失败'
        console.error('❌ Navigation Store - getOfficeCollectList API调用失败:', officeCollectResponse.reason)
      }

      // 恢复原始数据源状态
      currentDataSource.value = originalDataSource

      // 判断整体成功状态
      const successCount = [
        results.menuList.success,
        results.officeList.success,
        results.collectList.success,
        results.officeCollectList.success
      ].filter(Boolean).length

      results.success = successCount > 0
      results.message = `API数据刷新完成：${successCount}/4 个接口成功更新`

      await autoBackupManager.performBackup()
      return results

    } catch (error) {
      console.error('🔄 Navigation Store - 强制刷新所有API数据失败:', error)
      return {
        success: false,
        menuList: { success: false, error: error.message },
        officeList: { success: false, error: error.message },
        collectList: { success: false, error: error.message },
        officeCollectList: { success: false, error: error.message },
        message: `刷新失败: ${error.message}`
      }
    }
  }

  // 强制初始化并保存单个数据源到 localStorage（忽略现有数据）
  async function forceInitAndSaveDataSource(dataSource, apiData) {
    try {

      // 临时切换数据源以确保保存到正确的 localStorage 键
      const originalDataSource = currentDataSource.value
      currentDataSource.value = dataSource

      // 临时保存当前的 categoryApps 状态
      const originalCategoryApps = { ...categoryApps.value }

      // 清空 categoryApps 准备处理新数据源
      Object.keys(categoryApps.value).forEach(key => {
        delete categoryApps.value[key]
      })

      // 处理 API 数据并初始化分类
      if (apiData && apiData.data && apiData.data.length > 0) {
        apiData.data.forEach(categoryData => {
          const categoryType = categoryData.type
          if (categoryData.children) {
            const processedApps = categoryData.children.map(child => {
              // 处理类型转换：collect -> collection
              let appType = child.webType || 'app'
              if (appType === 'collect') {
                appType = 'collection'
              }

              // 处理文件夹和合集
              if (child.webType === 'folder' || child.webType === 'collection' || child.webType === 'collect') {
                // 处理子项
                const folderChildren = Array.isArray(child.children) ?
                  child.children.map(subChild => ({
                    id: subChild.id,
                    name: subChild.name,
                    icon: subChild.logo,
                    color: subChild.color || '#ffffff',
                    category: categoryType,
                    size: { w: parseInt(subChild.w || 1), h: parseInt(subChild.h || 1) },
                    url: subChild.websiteAddress ? `https://${subChild.websiteAddress}` : '',
                    type: subChild.webType || 'app',
                    description: subChild.descs || '',
                    x: parseInt(subChild.x || 0),
                    y: parseInt(subChild.y || 0),
                    websiteAddress: subChild.websiteAddress,
                    iscanopen: subChild.iscanopen,
                    isfixed: subChild.isfixed
                  })) : []

                return {
                  id: (child.webType === 'collection' || child.webType === 'collect')
                    ? `collection-${child.id || `${child.name}-${Math.round(Math.random() * 10000)}`}`
                    : `folder-${child.id || `${child.name}-${Math.round(Math.random() * 10000)}`}`,
                  name: child.type || child.name || ((child.webType === 'collection' || child.webType === 'collect') ? '应用合集' : '文件夹'),
                  icon: child.logo || child.icon || ((child.webType === 'collection' || child.webType === 'collect') ? '📦' : '📁'),
                  color: child.color || '#ffffff',
                  category: categoryType,
                  size: { w: parseInt(child.w || 2), h: parseInt(child.h || 2) },
                  type: appType,
                  children: folderChildren,
                  description: `包含${folderChildren.length}个应用`,
                  originalId: child.id,
                  x: parseInt(child.x || 0),
                  y: parseInt(child.y || 0),
                  websiteAddress: child.websiteAddress,
                  iscanopen: child.iscanopen,
                  isfixed: child.isfixed
                }
              } else {
                // 处理普通应用
                return {
                  id: child.id,
                  name: child.name,
                  icon: child.logo,
                  color: child.color || '#ffffff',
                  category: categoryType,
                  size: { w: parseInt(child.w || 1), h: parseInt(child.h || 1) },
                  url: child.websiteAddress ? `https://${child.websiteAddress}` : '',
                  type: appType,
                  description: child.descs || '',
                  x: parseInt(child.x || 0),
                  y: parseInt(child.y || 0),
                  websiteAddress: child.websiteAddress,
                  iscanopen: child.iscanopen,
                  isfixed: child.isfixed
                }
              }
            })

            // 设置分类应用
            categoryApps.value[categoryType] = processedApps
          }
        })

        // 强制保存到 localStorage（覆盖现有数据）
        saveAllCategoryData()
      }

      // 恢复原始状态
      currentDataSource.value = originalDataSource
      Object.keys(categoryApps.value).forEach(key => {
        delete categoryApps.value[key]
      })
      Object.assign(categoryApps.value, originalCategoryApps)

    } catch (error) {
      console.error(`Navigation Store: 强制初始化并保存 ${dataSource} 数据源失败:`, error)
      throw error
    }
  }

  // 强制更新收藏数据
  async function forceUpdateCollectData(dataSource, apiResponse) {
    try {

      if (apiResponse && apiResponse.status === 200 && Array.isArray(apiResponse.data)) {
        const processedData = apiResponse.data.map(item => ({
          id: item.id,
          name: item.name,
          icon: item.logo,
          color: item.color,
          description: item.descs,
          url: item.websiteAddress,
          websiteAddress: item.websiteAddress,
          iscanopen: item.iscanopen,
          isfixed: item.isfixed
        }))

        // 确定存储键
        const storageKey = dataSource === 'office' ? 'officeDockApps' : 'homeDockApps'

        // 强制保存到localStorage（覆盖现有数据）
        localStorage.setItem(storageKey, JSON.stringify(processedData))

        // 如果当前数据源匹配，更新响应式数据
        if (currentDataSource.value === dataSource) {
          collectAppsData.value = processedData
        }

        // 触发localStorage变化事件
        window.dispatchEvent(new CustomEvent('dockAppsUpdated', {
          detail: {
            dataSource: dataSource,
            storageKey: storageKey,
            data: processedData
          }
        }))

      } else {
        throw new Error(`API返回数据异常: ${JSON.stringify(apiResponse)}`)
      }
    } catch (error) {
      console.error(`Navigation Store: 强制更新 ${dataSource} 收藏数据失败:`, error)
      throw error
    }
  }

  return {
    // 状态
    allNavItems,
    currentCategory,
    categoryApps,
    isLoading,
    allApps,
    specialCardsMaster,
    currentDataSource,
    dataSourceCache,
    startupMode,

    // 计算属性
    categories,
    currentApps,
    currentCategoryData,

    // 方法
    initNavigation,
    switchCategory,
    loadCategoryApps,
    saveCategoryData,
    addApp,
    removeApp,
    updateAppOrder,
    refreshCollectAppsData,
    getCollectStorageKey,
    setAllApps,
    setSpecialCards,
    setDataSource,
    updateDataSource,
    getAppById,
    updateApp,
    moveAppToFolder,
    saveAllCategoryData,
    loadAllCategoryData,
    initCategoryData,
    initCollectAppsFromAPI,
    preloadAllDataSources,
    isUserLoggedIn,
    loadBackupDataForLoggedInUser,
    loadCollectAppsDataFromBackup,
    loadUserDataSources,
    resetToUserData,
    initAndSaveDataSource,
    refreshAllApiData,
    forceInitAndSaveDataSource,
    forceUpdateCollectData,
    setStartupMode
  }
})
