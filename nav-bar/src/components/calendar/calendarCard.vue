<template>
    <div class="calendar-card-container" :style="containerStyle" @mouseenter="isHovering = true" @mouseleave="isHovering = false">
        <!-- 显示今日日期信息的卡片 -->
         <div v-if="gridStyle == '2x1'" class="calendar-card size-2x1" @click.stop="showModal = true" v-bind="$attrs">
                <div class="days-passed">今年已过 {{ daysPassed }} 天</div>
        </div>
        <div v-else-if="gridStyle == '2x2'" class="calendar-card size-2x2" @click.stop="showModal = true" v-bind="$attrs">
            <div class="card-body" style="padding: 0;">
                <div class="date-number">{{ currentDay }}</div>
                <div class="weekday">{{ weekdayName }}</div>
            </div>
        </div>
         <div v-else-if="gridStyle == '3x2'" class="calendar-card size-3x2" @click.stop="showModal = true" v-bind="$attrs">
            <div class="card-body" style="padding: 0;">
                <div class="date-number">{{ currentDay }}</div>
                <div class="weekday">{{ weekdayName }}</div>
            </div>
        </div>
           <div v-else-if="gridStyle == '3x3'" class="calendar-card size-3x3" @click.stop="showModal = true" v-bind="$attrs">
            <div class="card-body" style="padding: 0;">
                <div class="date-number">{{ currentDay }}</div>
                <div class="weekday">{{ weekdayName }}</div>
            </div>
        </div>
         <div v-else-if="gridStyle == '4x2'" class="calendar-card size-4x2" @click.stop="showModal = true" v-bind="$attrs">
            <div class="card-body" style="padding: 0;">
                <div class="date-number">{{ currentDay }}</div>
                <div class="weekday">{{ weekdayName }}</div>
            </div>
        </div>
        <div v-else class="calendar-card" @click.stop="showModal = true" v-bind="$attrs">
            <div class="card-header" :style="headerStyle">
                <span>{{ currentMonth }}</span>
                <span class="year">{{ currentYear }}</span>
            </div>
            <div class="card-body">
                <div class="date-number">{{ currentDay }}</div>
                <div class="weekday">{{ weekdayName }}</div>
                <div class="days-passed">今年已过 {{ daysPassed }} 天</div>
            </div>
        </div>
    </div>
    
    <!-- 使用Teleport将弹窗内容传送到body -->
    <Teleport to="body">
        <div v-if="showModal" class="modal-overlay" @click.self="showModal = false">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>{{ title || '日历' }}</h3>
                    <button class="close-btn" @click="showModal = false" style="outline: none;border: none;">
                        <img :src="closeSvg" alt="close" />
                    </button>
                </div>
                <div class="modal-body">
                    <baidu-calendar @change="handleDateChange" :date="selectedDate" :range="yearRange" @ready="calendarReady"/>
                </div>
            </div>
        </div>
    </Teleport>
</template>
    
<script setup>
import { ref, computed, onMounted, useAttrs, watch } from "vue";
import  closeSvg  from '@/assets/modal/close.svg'

// 定义属性
const props = defineProps({
  url: { type: String, default: null },
  appId: { type: String, default: '' },
  title: { type: String, default: '日历' },
  headerColor: { type: String, default: '#FF6B6B' },
  size: {
    type: Object,
    default: () => ({ w: 2, h: 2 })
  }
});

watch(() => props.size, (newSize, oldSize) => {
  gridStyle.value = newSize.w + 'x' + newSize.h
})


const gridStyle = ref()

// 状态
const showModal = ref(false);
const isHovering = ref(false); // 新增：鼠标悬停状态
const today = ref(new Date());
const isCalendarLoading = ref(true); // 新增：日历加载状态
// const selectedDate = ref(""); // 选中的日期
const yearRange = [2015, 2025]; // 年份范围

// 计算样式
const headerStyle = computed(() => ({
  background: props.headerColor ? `linear-gradient(145deg, ${props.headerColor} 0%, ${lightenColor(props.headerColor, 20)} 100%)` : 'linear-gradient(145deg, #FF6B6B 0%, #FF8E53 100%)'
}));

const containerStyle = computed(() => ({
  width: '100%',
  height: '100%'
}));

// 辅助函数 - 提亮颜色
function lightenColor(color, percent) {
  // 简单实现，只处理十六进制颜色
  if (!color || color.indexOf('#') !== 0) return '#FF8E53';
  
  let r = parseInt(color.slice(1, 3), 16);
  let g = parseInt(color.slice(3, 5), 16);
  let b = parseInt(color.slice(5, 7), 16);
  
  r = Math.min(255, r + (255 - r) * (percent / 100));
  g = Math.min(255, g + (255 - g) * (percent / 100));
  b = Math.min(255, b + (255 - b) * (percent / 100));
  
  return `#${Math.round(r).toString(16).padStart(2, '0')}${Math.round(g).toString(16).padStart(2, '0')}${Math.round(b).toString(16).padStart(2, '0')}`;
}

// 处理日期变更
const handleDateChange = (obj) => {
    
    //selectedDate.value = obj.date;
};

// 日历加载完成回调
const calendarReady = () => {
    // 延迟一小段时间再隐藏loading，确保渲染完成
    setTimeout(() => {
        isCalendarLoading.value = false;
    }, 500);
};

// 监听弹窗显示状态变化
watch(showModal, (newVal) => {
    if (newVal) {
        // 弹窗打开时重置loading状态
        isCalendarLoading.value = true;
    }
});

// 计算当前日期信息
const currentDay = computed(() => today.value.getDate());
const currentMonth = computed(() => {
    const months = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
    return months[today.value.getMonth()];
});
const currentYear = computed(() => today.value.getFullYear());

// 计算星期几
const weekdayName = computed(() => {
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    return weekdays[today.value.getDay()];
});

// 计算今年已过天数
const daysPassed = computed(() => {
    const start = new Date(today.value.getFullYear(), 0, 1);
    const diff = today.value - start;
    return Math.floor(diff / (1000 * 60 * 60 * 24)) + 1;
});

// 每天0点更新日期
onMounted(() => {
    gridStyle.value = props.size.w + 'x' + props.size.h
    // 计算到明天0点的毫秒数
    const now = new Date();
    const tomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
    const timeToMidnight = tomorrow - now;
    
    // 设置定时器，凌晨自动更新日期
    setTimeout(() => {
        today.value = new Date();
        // 之后每24小时更新一次
        setInterval(() => {
            today.value = new Date();
        }, 24 * 60 * 60 * 1000);
    }, timeToMidnight);
});
</script>
    
<style lang="scss" scoped>
.calendar-card-container {
    width: 100%;
    height: 100%;
}

.calendar-card {
    width: 100%;
    height: 100%;
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    flex-direction: column;
}

.calendar-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(145deg, #FF6B6B 0%, #FF8E53 100%);
    color: white;
    padding: 10px 15px;
    font-weight: bold;
    font-size: 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    position: relative; /* 为设置按钮定位添加 */
}

/* 设置按钮样式 */
.settings-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    transition: all 0.2s;
    padding: 0;
    z-index: 2;
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: translateY(-50%) scale(1.1);
}

.settings-btn svg {
    width: 14px;
    height: 14px;
    stroke: white;
}

.year {
    font-size: 14px;
    opacity: 0.9;
}

.card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 0 20px 0;
}

.date-number {
    width: 90px;
    height: 90px;
    background: #71C6FF;
    color: #fff;
    border-radius: 50%;
    font-size: 50px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.15);
}

.weekday {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.days-passed {
    font-size: 12px;
    color: #888;
    margin-top: 2px;
}



.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(231, 76, 60, 0.2);
    border-top-color: rgba(231, 76, 60, 1);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

.loading-text {
    font-size: 14px;
    color: #666;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.size-2x1{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #F5655A 0%, #FF8E53 100%);
    color: white !important;
    .days-passed{
        color: white;
    }
}
.size-2x2{
    .date-number{
        width: 60%;
        height: 60%;
    }
}
.size-3x2{
    .date-number{
        width: 40%;
        height: 60%;
    }
}
.size-4x2{
    .date-number{
        width: 30%;
        height: 60%;
    }
}
.size-4x3{
    .card-body{
        padding: 10px;
    }
}
</style>

<!-- 使用全局样式确保弹窗在任何地方都能正确显示 -->
<style>
/* 自定义弹窗样式 - 注意这里没有使用scoped，这样样式会应用到teleport的目标位置 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999; /* 确保在最顶层 */
    animation: fadeIn 0.3s ease; /* 添加淡入动画 */
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 700px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: scaleIn 0.3s ease; /* 添加缩放动画 */
    transform-origin: center;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.modal-header h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.close-btn {
    background: transparent;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
}

.close-btn:hover {
    /* background-color: #f0f0f0; */
    color: #333;
}

.modal-body {
    padding: 16px;
    overflow: hidden;
}


.op-calendar-pc-table tbody tr {
    color: black;
}
.op-calendar-pc-select-box{
    display: none;

}
.op-calendar-pc-left{
    padding: 10px !important;
}

/* 添加淡入动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 添加缩放动画 */
@keyframes scaleIn {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}
</style>
    