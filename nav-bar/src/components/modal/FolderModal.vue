<template>
  <div class="app-modal-overlay" v-if="visible" @click.self="handleOverlayClick">
    <div class="app-modal" :class="{'app-modal-fullscreen': isFullscreen}">
      <div class="app-modal-header">
        <div class="app-modal-title-container">
          <!-- 编辑模式：显示输入框 -->
          <input
            v-if="isEditingTitle"
            v-model="editingTitleValue"
            @keydown="handleTitleKeydown"
            @blur="saveTitle"
            ref="titleInputRef"
            class="title-input"
            type="text"
            maxlength="20"
          />
          <!-- 显示模式：显示title和编辑图标 -->
          <div v-else class="title-display">
            <span class="app-modal-title">{{ title }}</span>
              <svg  @click="startEditTitle" t="1750816215062" class="icon edit-title-btn" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4583" width="20" height="20"><path d="M846 792H142c-4.4 0-8 3.6-8 8v40c0 4.4 3.6 8 8 8h704c4.4 0 8-3.6 8-8v-40c0-4.4-3.6-8-8-8zM194.7 726.4l157.4-41.5c4.1-1.1 7.8-3.2 10.8-6.2l357.5-357.5c9.4-9.4 9.4-24.6 0-33.9L614.3 181c-9.4-9.4-24.6-9.4-33.9 0L222.9 538.5c-3 3-5.2 6.7-6.2 10.8l-41.5 157.4c-3.2 12 7.6 22.8 19.5 19.7z m62.5-91.8l16.6-63.2c0.7-2.7 2.2-5.3 4.2-7.3l312.3-312.4c3.1-3.1 8.2-3.1 11.3 0l48.1 48.1c3.1 3.1 3.1 8.2 0 11.3L337.3 623.5c-2 2-4.5 3.4-7.2 4.2L267 644.4c-5.9 1.5-11.3-3.9-9.8-9.8z" fill="#333333" p-id="4584"></path></svg>
          </div>
        </div>
        <div class="app-modal-spacer"></div>
        <div class="app-modal-controls">
          <button class="control-btn close-btn" @click="handleCloseButtonClick" title="关闭">
            <img :src="closeSvg" alt="close" />
          </button>
        </div>
      </div>
      <div class="app-modal-content">
        <!-- 文件夹模态框内容 -->
        <div class="folder-modal-content">
          <!-- 显示文件夹内的应用 -->
          <div v-if="folder && folder.children && folder.children.length > 0" class="folder-modal-grid">
            <!-- 新增图标按钮 -->
            <div class="folder-app-item add-app-item" @click="showAddAppModal">
              <div class="folder-app-icon add-app-icon">
                <img :src="customAdd" />
              </div>
              <div class="folder-app-name">新增图标</div>
              
            </div>

            <!-- 现有应用列表 -->
            <div
              v-for="(app, index) in folder.children"
              :key="app.id || `folder-app-${index}-${Date.now()}`"
              class="folder-app-item"
              @click="openApp(app)"
              @contextmenu.prevent.stop="showFolderContextMenu($event, app)"
              draggable="true"
              @dragstart="handleDragStart($event, app)"
            >
              <div class="folder-app-icon" :style="{'background-color': app.color || '#ffffff'}">
                <img v-if="app.icon" :src="concatUrl(app.icon)" alt="icon" />
                <div v-else class="app-icon-div">
                  <span v-text-scale="app.name.slice(0, 6)" class="app-icon-text">{{ app.name.slice(0, 6) }}</span>
                </div>
                <div v-if="app.iscanopen == 1" class="newWindow">
                  <img :src="inside" class="inside" />
              </div>
              </div>
              <div class="folder-app-name">{{ app.name }}</div>
            </div>
          </div>

          <!-- 空文件夹提示 -->
          <div v-else class="empty-folder">
            <!-- 新增图标按钮 -->
            <div class="add-app-button" @click="showAddAppModal">
              <div class="add-app-icon-large">
                <div class="add-icon-large">+</div>
              </div>
              <div class="add-app-text">新增图标</div>
              <div class="add-app-desc">点击添加应用到文件夹</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 应用选择弹窗 -->
    <div class="app-select-overlay" v-if="showAppSelect" @click.self="hideAppSelect">
      <div class="app-select-modal">
        <div class="app-select-header">
          <div class="app-select-title">选择应用</div>
          <button class="app-select-close" @click="hideAppSelect">
            <img :src="closeSvg" alt="close" />
          </button>
        </div>
        <div class="app-select-content" @scroll="handleScroll" ref="appSelectContainerRef">
          <div v-if="loading && appList.length === 0" class="loading-spinner">
            <div class="spinner"></div>
            <div>加载中...</div>
          </div>
          <div v-if="appList.length === 0 && !loading" class="empty-apps">暂无应用数据</div>
          <div class="app-select-grid">
            <div
              v-for="app in appList"
              :key="app.id"
              class="app-select-item"
              @click="selectApp(app)"
            >
              <div class="app-card-header">
                <div class="app-icon-wrapper">
                  <img v-if="app.logo" :src="concatUrl(app.logo)" alt="app-icon" class="app-icon-img" />
                  <div v-else class="app-icon-div">
                    <span v-text-scale="app.name.slice(0, 6)" class="app-icon-text">{{ app.name.slice(0, 6) }}</span>
                  </div>
                </div>
                <div class="app-title-area">
                  <div class="app-name-text" :title="app.name">{{ app.name.length > 7 ? app.name.slice(0, 7) + '...' : app.name }}</div>
                  <div class="app-category">应用</div>
                </div>
              </div>
              <div class="app-card-content">
                <div class="app-description-text">{{ app.descs }}</div>
              </div>
              <div class="app-card-footer">
                <button type="primary">添加</button>
              </div>
            </div>
          </div>
          <div v-if="loading && appList.length > 0" class="loading-more">
            <div class="spinner-small"></div>
            <span>加载更多...</span>
          </div>
          <div v-if="!hasMore && appList.length > 0" class="no-more">已加载全部应用</div>
        </div>
      </div>
    </div>

    <!-- 文件夹应用右键菜单 -->
    <div
      ref="folderContextMenu"
      v-show="showFolderMenu"
      :style="folderMenuPosition"
      @click.stop
      class="folder-context-menu"
    >
      <div class="folder-menu-item" @click="deleteAppFromFolder">
        <!-- <i class="folder-menu-icon">🗑️</i> -->
        <span class="folder-menu-label">删除</span>
      </div>
      <div class="folder-menu-item" @click="addAppToDesktop">
        <!-- <i class="folder-menu-icon">🖥️</i> -->
        <span class="folder-menu-label">添加到桌面</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { useUrlStore } from '@/stores/url';
import { useSettingStore } from '@/stores/setting.js';
import { getAllAppList } from '@/api/navbar';
import { message, Modal } from 'ant-design-vue';
import  closeSvg  from '@/assets/modal/close.svg'
import { saveFolderApp } from '@/api/folder'
import customAdd from '@/assets/icons/customAdd.svg'
import inside from '@/assets/icons/inside.svg'
const urlStore = useUrlStore();
const settingStore = useSettingStore();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  folder: {
    type: Object,
    default: null
  },
  title: {
    type: String,
    default: '应用'
  }
});


console.log(props.folder,'props.folder')
const emit = defineEmits(['close', 'update:visible', 'open-app', 'drag-start', 'add-app-to-folder', 'update-title', 'remove-app-from-folder', 'add-app-to-desktop']);

const isFullscreen = ref(false);
const isMinimized = ref(false);

// 标题编辑相关状态
const isEditingTitle = ref(false);
const editingTitleValue = ref('');
const titleInputRef = ref(null);

// 应用选择相关状态
const showAppSelect = ref(false);
const appList = ref([]);
const loading = ref(false);
const hasMore = ref(true);
const page = ref(1);
const pageSize = ref(30);
const appSelectContainerRef = ref(null);

// 文件夹右键菜单相关状态
const showFolderMenu = ref(false);
const folderMenuPosition = ref({ top: '0px', left: '0px' });
const activeFolderApp = ref(null);
const folderContextMenu = ref(null);

// 监听visible变化，重置全屏状态
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时确保退出全屏
    if (isFullscreen.value) {
      toggleFullscreen();
    }
    // 重置最小化状态
    if (isMinimized.value) {
      isMinimized.value = false;
    }
  }
});

// 添加一个函数来正确拼接URL
const concatUrl = (path) => {
  
  // 如果path为null那么就随机a-z加上.png
  return urlStore.concatUrl(path)
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

const handleOverlayClick = () => {
  if (settingStore.closeModalOnOutsideClick) {
    handleClose();
  }
};

const handleCloseButtonClick = () => {
  if (settingStore.closeModalOnButtonClick) {
    handleClose();
  }
};

// 开始编辑标题
const startEditTitle = () => {
  editingTitleValue.value = props.title;
  isEditingTitle.value = true;
  // 下一帧聚焦输入框
  nextTick(() => {
    if (titleInputRef.value) {
      titleInputRef.value.focus();
      titleInputRef.value.select();
    }
  });
};

// 保存标题
const saveTitle = () => {
  const newTitle = editingTitleValue.value.trim();
  if (newTitle && newTitle !== props.title) {
    emit('update-title', newTitle);
  }
  isEditingTitle.value = false;
};

// 取消编辑标题
const cancelEditTitle = () => {
  isEditingTitle.value = false;
  editingTitleValue.value = '';
};

// 处理标题输入框键盘事件
const handleTitleKeydown = (event) => {
  if (event.key === 'Enter') {
    event.preventDefault();
    saveTitle();
  } else if (event.key === 'Escape') {
    event.preventDefault();
    cancelEditTitle();
  }
};

// 最小化弹窗
const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value;
  // 如果已经最小化，可以添加动画或其他效果
};

// 切换全屏状态
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  
  // 触发全局事件，通知布局组件修改z-index
  window.dispatchEvent(new CustomEvent('folder-modal-fullscreen', {
    detail: { isFullscreen: isFullscreen.value }
  }));
  
  // 添加或移除body类以禁用滚动
  if (isFullscreen.value) {
    document.body.classList.add('modal-fullscreen-active');
  } else {
    document.body.classList.remove('modal-fullscreen-active');
  }
};

// 打开应用
const openApp = (app) => {
  emit('open-app', app);
};

// 处理拖拽开始
const handleDragStart = (event, app) => {
  emit('drag-start', event, app, true);
};

// 显示应用选择弹窗
const showAddAppModal = () => {
  showAppSelect.value = true;
  // 重置分页状态
  page.value = 1;
  hasMore.value = true;
  appList.value = [];
  // 加载应用列表
  getAllAppFunc();
};

// 隐藏应用选择弹窗
const hideAppSelect = () => {
  showAppSelect.value = false;
};

// 获取应用列表
const getAllAppFunc = async () => {
  if (!hasMore.value || loading.value) return;

  loading.value = true;
  try {
    const res = await getAllAppList(page.value, pageSize.value);

    // 当是第一页时替换数据，否则追加数据
    if (page.value === 1) {
      appList.value = res.data || [];
    } else {
      appList.value = [...appList.value, ...(res.data || [])];
    }

    // 判断是否还有更多数据
    hasMore.value = res.data && res.data.length === pageSize.value;

    // 增加页码，为下次加载准备
    if (res.data && res.data.length > 0) {
      page.value++;
    }

  } catch (error) {
    message.error('获取应用列表失败');
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 处理滚动加载更多
const handleScroll = (event) => {
  const container = event.target;
  if (container.scrollTop + container.clientHeight >= container.scrollHeight - 10) {
    getAllAppFunc();
  }
};

// 选择应用添加到文件夹
const selectApp = async (app) => {
  console.log('🔍 FolderModal: 选择应用', { appName: app.name, appId: app.id, folder: props.folder?.name });

  const token = localStorage.getItem('token')
  if(token) {
    const folderId = props.folder.id
    const appId = app.id
    await saveFolderApp(folderId,appId).then((res) => {
      if(res.status == 200) {
        console.log('✅ API: 应用添加成功')
      }
    })
  }

  // 构建应用对象 - 保留原始信息，不生成新ID
  const folderApp = {
    // 使用原始ID，如果没有则生成一个基于应用信息的稳定ID
    id: app.id || `app-${app.name.replace(/\s+/g, '-').toLowerCase()}-${Date.now()}`,
    name: app.name,
    type: 'app',
    icon: app.logo,
    url: app.websiteAddress ? `https://${app.websiteAddress}` : '',
    description: app.descs || '',
    color: app.color || '#f5f5f5',
    iscanopen: app.iscanopen || 1,
    // 保留原始应用信息用于匹配
    originalAppId: app.id,
    websiteAddress: app.websiteAddress
  };

  console.log('📤 FolderModal: 发送应用数据到父组件', folderApp);

  // 发出事件，通知父组件添加应用到文件夹
  emit('add-app-to-folder', folderApp);

  // 关闭应用选择弹窗
  // hideAppSelect();

  // message.success(`已添加 ${app.name} 到文件夹`);
};

// 显示文件夹应用右键菜单
const showFolderContextMenu = (event, app) => {
  event.preventDefault();
  event.stopPropagation();

  // 记录当前选中的应用
  activeFolderApp.value = app;

  // 计算菜单位置
  const rect = event.currentTarget.getBoundingClientRect();
  const menuWidth = 120; // 预估菜单宽度
  const menuHeight = 80; // 预估菜单高度

  let left = event.clientX;
  let top = event.clientY;

  // 防止菜单超出视窗边界
  if (left + menuWidth > window.innerWidth) {
    left = window.innerWidth - menuWidth - 10;
  }
  if (top + menuHeight > window.innerHeight) {
    top = window.innerHeight - menuHeight - 10;
  }

  folderMenuPosition.value = {
    top: `${top}px`,
    left: `${left}px`
  };

  // 显示菜单
  showFolderMenu.value = true;
};

// 隐藏文件夹应用右键菜单
const hideFolderContextMenu = () => {
  showFolderMenu.value = false;
  activeFolderApp.value = null;
};

// 从文件夹中删除应用
const deleteAppFromFolder = () => {
  if (!activeFolderApp.value || !props.folder) return;

  Modal.confirm({
    title: '确认删除',
    content: `确定要从文件夹中删除 ${activeFolderApp.value.name} 吗？`,
    okText: '确认',
    cancelText: '取消',
    centered: true,
    onOk() {
      // 从文件夹children中移除应用
      const appIndex = props.folder.children.findIndex(app => app.id === activeFolderApp.value.id);
      if (appIndex !== -1) {
        props.folder.children.splice(appIndex, 1);

        // 发出事件通知父组件更新数据
        emit('remove-app-from-folder', activeFolderApp.value);

        message.success(`已从文件夹中删除 ${activeFolderApp.value.name}`);
      }

      // 关闭菜单
      hideFolderContextMenu();
    }
  });
};

// 将应用添加到桌面（当前分类）
const addAppToDesktop = () => {
  if (!activeFolderApp.value || !props.folder) return;

  // 从文件夹children中移除应用
  const appIndex = props.folder.children.findIndex(app => app.id === activeFolderApp.value.id);
  if (appIndex !== -1) {
    const appToMove = props.folder.children[appIndex];
    props.folder.children.splice(appIndex, 1);

    // 发出事件通知父组件将应用添加到当前分类
    emit('add-app-to-desktop', appToMove);

    message.success(`已将 ${activeFolderApp.value.name} 添加到桌面`);
  }

  // 关闭菜单
  hideFolderContextMenu();
};

// 处理全局点击事件，关闭右键菜单
const handleGlobalClick = (event) => {
  if (showFolderMenu.value && folderContextMenu.value && !folderContextMenu.value.contains(event.target)) {
    hideFolderContextMenu();
  }
};

// 组件挂载时添加全局点击事件监听
onMounted(() => {
  document.addEventListener('click', handleGlobalClick);
});

// 组件卸载时移除全局点击事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick);
});
</script>

<style scoped>
.app-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 500;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.app-modal {
  width: 90%;
  min-height: 200px;
  height: max-content;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 700px;
  position: relative;
  z-index: 501;
}

.app-modal-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0;
  z-index: 9999; /* 更高的z-index确保覆盖所有元素 */
}

.app-modal-overlay:has(.app-modal-fullscreen) {
  background-color: #fff;
  backdrop-filter: none;
  z-index: 9998; /* 提高overlay的z-index */
}

.app-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 53px;
  -webkit-app-region: drag; /* 允许拖动窗口 */
}

.app-modal-title-container {
  flex: 1;
  display: flex;
  align-items: center;
}

.title-display {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.app-modal-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  /* flex: 1; */
  user-select: none; /* 防止选中文本 */
}

.edit-title-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  opacity: 0;
  transition: all 0.2s ease;
  -webkit-app-region: no-drag; /* 编辑按钮区域不可拖动窗口 */
  svg{
    width: 20px;
    height: 20px;
  }
}

.title-display:hover .edit-title-btn {
  opacity: 0.6;
}

.edit-title-btn:hover {
  opacity: 1 !important;
  background-color: rgba(0, 0, 0, 0.1);
}

.edit-icon {
  width: 12px;
  height: 12px;
  color: #666;
}

.title-input {
  /* flex: 1; */
  font-size: 13px;
  font-weight: 500;
  color: #333;
  border: 1px solid #4285F4;
  border-radius: 4px;
  padding: 4px 8px;
  background: #fff;
  outline: none;
  -webkit-app-region: no-drag; /* 输入框区域不可拖动窗口 */
}

.title-input:focus {
  border-color: #4285F4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.app-modal-spacer {
  width: 60px; /* 与控制按钮区域保持对称 */
}

.app-modal-controls {
  display: flex;
  gap: 6px;
  margin-left: 4px;
  -webkit-app-region: no-drag; /* 控制按钮区域不可拖动窗口 */
}

.control-btn {
  width: 12px;
  height: 12px;
  padding: 5px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  outline: none;
  border: none;
}

.control-btn .icon {
  opacity: 0;
  width: 8px;
  height: 8px;
  transition: opacity 0.2s;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.app-modal:hover .control-btn .icon {
  opacity: 0.7;
}

.control-btn:hover .icon {
  opacity: 1 !important;
}

.minimize-btn {
  background-color: #ffbd2e;
}

.minimize-btn:hover {
  background-color: #ffbd2e;
  filter: brightness(0.9);
}

.maximize-btn {
  background-color: #28c940;
}

.maximize-btn:hover {
  background-color: #28c940;
  filter: brightness(0.9);
}

.app-modal-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #fafafa;
}

.icon {
  display: inline-block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-close {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
}

.icon-maximize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='5' y='5' width='14' height='14' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='8' y1='5' x2='8' y2='3'%3E%3C/line%3E%3Cline x1='16' y1='5' x2='16' y2='3'%3E%3C/line%3E%3Cline x1='5' y1='8' x2='3' y2='8'%3E%3C/line%3E%3Cline x1='5' y1='16' x2='3' y2='16'%3E%3C/line%3E%3C/svg%3E");
}

.icon-minimize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 文件夹模态框样式 */
.folder-modal-content {
  padding: 20px;
  min-height: 200px;
  height: max-content;
  box-sizing: border-box;
  overflow: auto;
}

.folder-modal-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 16px;
  padding: 10px;
}

.folder-app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 12px 8px;
  border-radius: 12px;
  position: relative;
}

.folder-app-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateY(-5px);
}

.folder-app-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  background-color: #f7f7f7;
  position: relative;
}

.folder-app-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.folder-app-name {
  font-size: 13px;
  text-align: center;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}

.empty-folder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: white;
  border-radius: 12px;
}

.empty-folder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #e0e0e0;
}

.empty-folder-text {
  font-size: 18px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.empty-folder-desc {
  font-size: 14px;
  color: #999;
  text-align: center;
}

/* 新增图标按钮样式 */
.add-app-item {
  /* border: 2px dashed #ddd !important; */
  background-color: #fafafa !important;
}


.add-app-icon {
  /* background-color: #f0f0f0 !important; */
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  font-size: 24px;
  color: #999;
  font-weight: bold;
}


/* 空文件夹时的新增按钮 */
.add-app-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 20px;
  border-radius: 12px;
  /* border: 2px dashed #ddd; */
  /* background-color: #fafafa; */
}

.add-app-button:hover {
  border-color: #4285F4;
  background-color: #f0f8ff;
}

.add-app-icon-large {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  background-color: #f0f0f0;
  border: 2px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.add-icon-large {
  font-size: 36px;
  color: #999;
  font-weight: bold;
}

.add-app-button:hover .add-icon-large {
  color: #4285F4;
}

.add-app-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.add-app-desc {
  font-size: 14px;
  color: #999;
}

/* 应用选择弹窗样式 */
.app-select-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.app-select-modal {
  width: 90%;
  max-width: 600px;
  height: 70%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  max-width: 1010px;
}

.app-select-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 53px;
}

.app-select-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.app-select-close {
  width: 12px;
  height: 12px;
  border: none;
  background: none;
  cursor: pointer;
  color: #999;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
  padding: 0px !important;
}


.app-select-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.app-select-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, 225px);
  gap: 10px;
  padding: 10px;
  justify-content: center; /* 让网格在容器中居中 */
}

.app-select-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 12px;
  border-radius: 12px;
  background-color: #f3f3f3;
  position: relative;
}

.app-select-item:hover {
  transform: translateY(-5px);
  background-color: rgba(0, 0, 0, 0.03);
}

.app-card-header {
  display: flex;
  position: relative;
}

.app-icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  margin-right: 12px;
  background-color: #f5f5f5;
  flex-shrink: 0;
  position: relative;
}

.app-icon-wrapper img {
  border-radius: 8px;
}

.app-icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.app-title-area {
  flex: 1;
  text-align: left;
}

.app-name-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.app-category {
  font-size: 12px;
  color: #888;
}

.app-card-content {
  padding: 10px 0px;
}

.app-description-text {
  font-size: 10px;
  color: #939393;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-align: left;
}

.app-card-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: auto;
  gap: 10px;
}

.app-card-footer div {
  width: 50px;
  height: 23px;
  background: #E9E9E9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-card-footer button {
  background: #85c4fa;
  width: 50px;
  height: 23px;
  border-radius: 4px;
  border: none;
  outline: none;
  color: #fff;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  padding: 0;
}

.app-card-footer button:hover {
  background: #0088ff;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #999;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f0f0f0;
  border-top: 2px solid #4285F4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #999;
  gap: 8px;
}

.spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid #f0f0f0;
  border-top: 2px solid #4285F4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.no-more {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.empty-apps {
  text-align: center;
  padding: 40px;
  color: #999;
  font-size: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 文件夹应用右键菜单样式 */
.folder-context-menu {
  position: fixed;
  z-index: 1001;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  max-width: 120px;
  overflow: visible;
  color: #333;
  backdrop-filter: blur(10px);
  animation: menuAppear 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-origin: top left;
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 13px;
  padding: 4px 0;
}

.folder-menu-item {
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0 12px;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
}

.folder-menu-item:hover {
  background-color: rgba(66, 133, 244, 0.1);
}

.folder-menu-icon {
  display: flex;
  margin-right: 8px;
  font-size: 14px;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.folder-menu-label {
  flex: 1;
  font-size: 13px;
}

@keyframes menuAppear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>

<style>
/* 确保i-carbon-folder-off图标显示 */
.i-carbon-folder-off {
  display: inline-block;
  width: 48px;
  height: 48px;
  background-color: #e0e0e0;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M30 12a2 2 0 0 0-2-2h-7.24l-1.79-2.68A2 2 0 0 0 17.25 6H8.05l1.6 2h7.6a2 2 0 0 1 1.67.93L20.71 12H28zM2.92 9.5l1.5 2L4 12v14a2 2 0 0 0 2 2h20a2 2 0 0 0 1.4-.59L29 26l-22.35-17L2.92 9.5zM27.4 25.6L6 9h-.19A2 2 0 0 0 4 11v15h23.8L6 10l21.6 15.8A2 2 0 0 1 27.4 25.6z'/%3E%3C/svg%3E");
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'%3E%3Cpath d='M30 12a2 2 0 0 0-2-2h-7.24l-1.79-2.68A2 2 0 0 0 17.25 6H8.05l1.6 2h7.6a2 2 0 0 1 1.67.93L20.71 12H28zM2.92 9.5l1.5 2L4 12v14a2 2 0 0 0 2 2h20a2 2 0 0 0 1.4-.59L29 26l-22.35-17L2.92 9.5zM27.4 25.6L6 9h-.19A2 2 0 0 0 4 11v15h23.8L6 10l21.6 15.8A2 2 0 0 1 27.4 25.6z'/%3E%3C/svg%3E");
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-position: center;
}
.newWindow{
  position: absolute;
  left: 0px;
  bottom: 0px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: none;
}
.newWindow .inside{
  border-radius: 0px 0px 0px 12px;
}
</style>
