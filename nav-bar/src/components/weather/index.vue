<template>
  <div class="weather-card" :class="[weatherType, { 'transitioning': isTransitioning }]">

    <div class="weather-animation">
      <transition name="weather-transition" mode="out-in" @before-enter="startTransition" @after-leave="endTransition">
        <!-- 晴天效果 -->
        <div v-if="weatherType === 'sunny'" :key="'sunny'" class="sunny-effect">
          <div class="sun">
            <div class="sun-rays"></div>
          </div>
          <div class="cloud small-cloud" style="top: 30%; left: 15%;"></div>
          <div class="cloud mini-cloud" style="top: 25%; left: 75%;"></div>
        </div>
        
        <!-- 雨天效果 -->
        <div v-else-if="weatherType === 'rainy'" :key="'rainy'" class="rainy-effect">
          <div class="cloud main-cloud"></div>
          <div class="cloud side-cloud" style="top: 15%; left: 25%; transform: scale(0.8);"></div>
          <div class="rain-effect-container">
            <div v-for="i in 40" :key="`rain-${i}`" class="raindrop" :style="{ 
              left: `${Math.random() * 100}%`, 
              animationDuration: `${0.7 + Math.random() * 0.9}s`,
              animationDelay: `${Math.random() * 2}s`,
              opacity: `${0.6 + Math.random() * 0.4}`
            }"></div>
          </div>
        </div>
        
        <!-- 多云效果 -->
        <div v-else-if="weatherType === 'cloudy'" :key="'cloudy'" class="cloudy-effect">
          <div class="cloud large-cloud cloud-1" style="top: 20%; left: 10%;"></div>
          <div class="cloud medium-cloud cloud-2" style="top: 35%; left: 55%;"></div>
          <div class="cloud small-cloud cloud-3" style="top: 15%; left: 40%;"></div>
          <div class="cloud mini-cloud cloud-4" style="top: 45%; left: 25%;"></div>
        </div>
        
        <!-- 雪天效果 -->
        <div v-else-if="weatherType === 'snowy'" :key="'snowy'" class="snowy-effect">
          <div class="cloud main-cloud"></div>
          <div class="cloud side-cloud" style="top: 15%; left: 25%; transform: scale(0.8);"></div>
          <div class="snow-effect-container">
            <div v-for="i in 50" :key="`snow-${i}`" class="snowflake" :style="{ 
              left: `${Math.random() * 100}%`, 
              top: `${Math.random() * -100}%`,
              animationDuration: `${4 + Math.random() * 6}s`,
              animationDelay: `${Math.random() * 3}s`,
              width: `${Math.max(3, Math.random() * 8)}px`,
              height: `${Math.max(3, Math.random() * 8)}px`
            }">
              <svg viewBox="0 0 24 24" fill="white">
                <path d="M12,0L14.45,7.4L21.85,7.4L15.7,12L18.15,19.4L12,14.8L5.85,19.4L8.3,12L2.15,7.4L9.55,7.4L12,0Z" />
              </svg>
            </div>
          </div>
        </div>
        
        <!-- 雷雨效果 -->
        <div v-else-if="weatherType === 'thunder'" :key="'thunder'" class="thunder-effect">
          <div class="thunder-clouds">
            <div class="cloud large-cloud thunder-cloud-1"></div>
            <div class="cloud medium-cloud thunder-cloud-2"></div>
          </div>
          <div class="lightning-container">
            <div class="lightning lightning-1"></div>
            <div class="lightning lightning-2"></div>
          </div>
          <div class="rain-effect-container">
            <div v-for="i in 40" :key="`thunder-rain-${i}`" class="raindrop" :style="{ 
              left: `${Math.random() * 100}%`, 
              animationDuration: `${0.7 + Math.random() * 0.9}s`,
              animationDelay: `${Math.random() * 2}s`,
              opacity: `${0.6 + Math.random() * 0.4}`
            }"></div>
          </div>
        </div>
      </transition>
    </div>
    
    <div class="weather-overlay"></div>
    
    <div class="weather-info size-2x1" v-if="gridStyle == '2x1'">
      <div class="weather-top">
        <div class="weather-icon">
          <i :class="weatherIconClass"></i>
        </div>
        <div class="weather-type-name">{{ weatherName }}</div>
      </div>
    </div>

    <div class="weather-info size-2x2" v-else-if="gridStyle == '2x2'">
      <div class="weather-top">
        <div class="weather-icon">
          <i :class="weatherIconClass"></i>
        </div>
        <div class="weather-type-name">{{ weatherName }}</div>
      </div>
      <div class="temperature">{{ temperature }}°</div>
    </div>

    <div class="weather-info size-3x2" v-else-if="gridStyle == '3x2'">
      <div class="weather-top">
        <div class="weather-icon">
          <i :class="weatherIconClass"></i>
        </div>
        <div class="weather-type-name">{{ weatherName }}</div>
      </div>
      <div class="temperature">{{ temperature }}°</div>
    </div>

      <div class="weather-info size-3x2" v-else-if="gridStyle == '3x3'">
      <div class="weather-top">
        <div class="weather-icon">
          <i :class="weatherIconClass"></i>
        </div>
        <div class="weather-type-name">{{ weatherName }}</div>
      </div>
      <div class="temperature">{{ temperature }}°</div>
      <div class="location-selector">
        <i class="fa-solid fa-location-dot location-icon"></i>
        <span>{{ location }}</span>
      </div>
    </div>

        <div class="weather-info size-4x2" v-else-if="gridStyle == '4x2'">
      <div class="weather-top">
        <div class="weather-icon">
          <i :class="weatherIconClass"></i>
        </div>
        <div class="weather-type-name">{{ weatherName }}</div>
      </div>
      <div class="temperature">{{ temperature }}°</div>
    </div>

    <div class="weather-info size-4x3" v-else>
      <div class="weather-top">
        <div class="weather-icon">
          <i :class="weatherIconClass"></i>
        </div>
        <div class="weather-type-name">{{ weatherName }}</div>
      </div>
      <div class="temperature">{{ temperature }}°</div>
      <div class="weather-bottom">
        <div class="location-selector">
          <i class="fa-solid fa-location-dot location-icon"></i>
          <span>{{ location }}</span>
        </div>
        <div class="weather-details">
          <div class="weather-detail">
            <i class="fa-solid fa-droplet"></i>
            <span>{{ humidity }}%</span>
          </div>
          <div class="weather-detail">
            <i class="fa-solid fa-wind"></i>
            <span>{{ windSpeed }}km/h</span>
          </div>
        </div>
      </div>
    </div>

    
    <!-- 加载中提示 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- 错误提示 -->
    <div v-if="error" class="error-message">{{ error }}</div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import '@fortawesome/fontawesome-free/css/all.min.css';
import { getWeather } from '@/api/weather.js';

const props = defineProps({
  size: {
    type: Object,
    default: () => ({ w: 2, h: 2 })
  }
});

watch(() => props.size, (newSize, oldSize) => {
  console.log(newSize,oldSize, 'wc')
  gridStyle.value = newSize.w + 'x' + newSize.h
})

console.log(props.size,'props.size')
const isTransitioning = ref(false);
const loading = ref(true);
const error = ref('');

console.log('[天气组件] 响应式变量初始化完成:', {
  loading: loading.value,
  error: error.value
});

// 统一缓存键
const WEATHER_CACHE_DATA = 'weather_cache_data';
const WEATHER_CACHE_TIME = 'weather_cache_time';
const WEATHER_CACHE_CITY = 'weather_cache_city';

// 城市设置相关
const selectedCity = ref(localStorage.getItem(WEATHER_CACHE_CITY) || '重庆');

// 天气数据
const temperature = ref(0);
const weatherCondition = ref('');
const location = ref('定位中...');
const humidity = ref(0);
const windSpeed = ref(0);
const gridStyle = ref()

// 根据API返回的天气代码映射到我们组件中的天气类型
const weatherTypeMap = {
  // 晴天
  '晴': 'sunny',
  '日间晴': 'sunny',
  '夜间晴': 'sunny',
  
  // 多云
  '多云': 'cloudy',
  '晴间多云': 'cloudy',
  '阴': 'cloudy',
  '部分多云': 'cloudy',
  
  // 雨天
  '小雨': 'rainy',
  '中雨': 'rainy',
  '大雨': 'rainy',
  '阵雨': 'rainy',
  '暴雨': 'rainy',
  '小到中雨': 'rainy',
  '中到大雨': 'rainy',
  '大到暴雨': 'rainy',
  '特大暴雨': 'rainy',
  '雨': 'rainy',
  
  // 雪天
  '小雪': 'snowy',
  '中雪': 'snowy',
  '大雪': 'snowy',
  '暴雪': 'snowy',
  '阵雪': 'snowy',
  '雪': 'snowy',
  '雨夹雪': 'snowy',
  
  // 雷雨
  '雷阵雨': 'thunder',
  '雷雨': 'thunder',
  '雷阵雨伴有冰雹': 'thunder',
  '强雷阵雨': 'thunder'
};

// 计算当前应该显示的天气类型
const weatherType = computed(() => {
  // 尝试从weatherTypeMap获取类型
  const type = weatherTypeMap[weatherCondition.value];
  // 如果找不到映射则返回默认值(cloudy)
  return type || 'cloudy';
});

// 根据天气类型获取天气名称
const weatherName = computed(() => {
  return weatherCondition.value || '未知';
});

// 根据天气类型获取图标类
const weatherIconClass = computed(() => {
  const weatherIcons = {
    sunny: 'fa-solid fa-sun',
    rainy: 'fa-solid fa-cloud-rain',
    cloudy: 'fa-solid fa-cloud',
    snowy: 'fa-solid fa-snowflake',
    thunder: 'fa-solid fa-cloud-bolt'
  };
  return weatherIcons[weatherType.value] || 'fa-solid fa-cloud';
});

// 处理过渡动画状态
function startTransition() {
  isTransitioning.value = true;
}

function endTransition() {
  isTransitioning.value = false;
}




/**
 * 获取天气数据
 * @param {boolean} forceUpdate - 是否强制更新，忽略缓存
 */
async function fetchWeatherData(forceUpdate = false) {
  console.log('[天气组件] fetchWeatherData 开始执行', { forceUpdate });

  try {
    // 检查缓存
    const cachedData = localStorage.getItem(WEATHER_CACHE_DATA);
    const cachedTime = localStorage.getItem(WEATHER_CACHE_TIME);

    console.log('[天气组件] 缓存检查:', {
      hasCachedData: !!cachedData,
      hasCachedTime: !!cachedTime,
      forceUpdate
    });

    if (cachedData && cachedTime && !forceUpdate) {
      const cacheAge = Date.now() - parseInt(cachedTime);
      const oneHour = 60 * 60 * 1000;

      console.log('[天气组件] 缓存年龄检查:', {
        cacheAge,
        oneHour,
        isValid: cacheAge < oneHour
      });

      if (cacheAge < oneHour) {
        console.log('[天气组件] 使用缓存的天气数据');
        const weatherData = JSON.parse(cachedData);
        updateWeatherDisplay(weatherData);
        loading.value = false;
        console.log('[天气组件] 缓存数据加载完成，loading设置为false');
        return;
      }
    }

    console.log('[天气组件] 准备发起后端API请求');

    loading.value = true;
    error.value = '';
    console.log('[天气组件] loading设置为true，开始API请求...');

    const response = await getWeather();

    console.log('[天气组件] 后端API请求完成，响应数据:', response);

    if (response && response.data) {
      response.data = JSON.parse(response.data)
      const weatherResult = response.data.results[0];
      console.log('[天气组件] 解析天气数据成功:', weatherResult);

      updateWeatherDisplay(weatherResult);

      // 更新当前城市选择
      selectedCity.value = weatherResult.location.name;

      // 使用统一的键更新缓存
      localStorage.setItem(WEATHER_CACHE_DATA, JSON.stringify(weatherResult));
      localStorage.setItem(WEATHER_CACHE_TIME, Date.now().toString());
      localStorage.setItem(WEATHER_CACHE_CITY, weatherResult.location.name);
      loading.value = false;
      console.log('[天气组件] 天气数据获取成功，loading设置为false:', weatherResult);
    } else {
      console.error('[天气组件] 后端API返回数据格式异常:', response);
      loading.value = false;
      throw new Error('获取天气数据失败');
    }
  } catch (err) {
    console.error('[天气组件] 天气API错误:', err);
    console.error('[天气组件] 错误详情:', {
      message: err.message,
      response: err.response?.data,
      status: err.response?.status
    });

    error.value = '无法获取天气信息';
    loading.value = false;
    console.log('[天气组件] 发生错误，loading设置为false，使用默认数据');

    temperature.value = 25;
    weatherCondition.value = '多云';
    location.value = selectedCity.value || '未知位置';
    humidity.value = 65;
    windSpeed.value = 12;
  } finally {
    loading.value = false;
    console.log('[天气组件] fetchWeatherData 执行完成，最终loading状态:', loading.value);
  }
}

/**
 * 更新天气显示数据
 * @param {Array} weatherResult - 心知天气API返回的`results`数组
 */
function updateWeatherDisplay(weatherResult) {
  console.log('[天气组件] updateWeatherDisplay 开始执行:', weatherResult);

  if (weatherResult) {
    const weatherData = weatherResult;
    console.log('[天气组件] 天气数据详情:', weatherData);

    if (weatherData && weatherData.now && weatherData.location) {
      console.log('[天气组件] 更新天气显示数据:', {
        temperature: weatherData.now.temperature,
        condition: weatherData.now.text,
        humidity: weatherData.now.humidity,
        windSpeed: weatherData.now.wind_speed,
        location: weatherData.location.name
      });

      temperature.value = weatherData.now.temperature;
      weatherCondition.value = weatherData.now.text;
      humidity.value = weatherData.now.humidity;
      windSpeed.value = weatherData.now.wind_speed;
      location.value = weatherData.location.name;

      console.log('[天气组件] 天气数据更新完成');
    } else {
      console.error('[天气组件] 天气数据结构异常:', {
        hasNow: !!weatherData?.now,
        hasLocation: !!weatherData?.location
      });
    }
  } else {
    console.error('[天气组件] weatherResult 为空');
  }
}

// 初始化组件
onMounted(() => {
  console.log('[天气组件] 组件开始挂载');
  console.log('[天气组件] props.size:', props.size);

  gridStyle.value = props.size.w + 'x' + props.size.h;
  console.log('[天气组件] gridStyle 设置为:', gridStyle.value);

  console.log('[天气组件] 初始loading状态:', loading.value);

  // 获取天气数据
  console.log('[天气组件] 开始初始化天气数据获取');
  fetchWeatherData();

  // 每小时刷新一次天气数据
  const interval = setInterval(() => {
    console.log('[天气组件] 定时刷新天气数据');
    fetchWeatherData();
  }, 60 * 60 * 1000);

  // 组件卸载时清除定时器
  return () => {
    console.log('[天气组件] 组件卸载，清除定时器');
    clearInterval(interval);
  };
});
</script>

<style lang="scss" scoped>
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.weather-card {
  position: relative;
  width: 100%;
  height: 100%;
  /* min-height: 160px; */
  border-radius: 16px;
  overflow: hidden;
  color: white;
  /* box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25); */
  font-family: 'Montserrat', 'Arial', sans-serif;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-style: preserve-3d;
  perspective: 1000px;
  .size-2x1{
    padding: 0px;
    @include flex-center();
  }
  .size-2x2,.size-3x2, .size-3x3, .size-4x2{
    padding: 0px;
    @include flex-center();
    .temperature{
      font-size: 35px;
      flex-grow: unset;
    }
  }
  .size-4x3{

      padding: 10px;
    
  }
}

.weather-card:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  transform: translateY(-5px);
}

.weather-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

/* 天气效果容器 */
.rain-effect-container,
.snow-effect-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

/* 天气切换过渡动画 */
.weather-transition-enter-active,
.weather-transition-leave-active {
  transition: all 0.8s ease;
}

.weather-transition-enter-from {
  opacity: 0;
  transform: scale(0.9) translateY(30px);
}

.weather-transition-leave-to {
  opacity: 0;
  transform: scale(1.1) translateY(-30px);
}

.transitioning {
  transition: background 1.5s ease;
}

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.weather-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
  z-index: 2;
}

.weather-info {
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
  height: 100%;
  box-sizing: border-box;
}

.weather-top {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;  
}

.weather-icon {
  font-size: 28px;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
}

.weather-type-name {
  font-size: 18px;
  font-weight: 500;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.temperature {
  font-size: 60px;
  font-weight: 700;
  margin: 0;
  text-shadow: 2px 2px 10px rgba(0, 0, 0, 0.5);
  align-self: center;
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.weather-bottom {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.weather-details {
  display: flex;
  justify-content: space-between;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 10px 15px;
  backdrop-filter: blur(5px);
}

.weather-detail {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.location-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  padding: 8px;
  border-radius: 20px;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  max-width: 230px;
}

.location-icon {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  flex-shrink: 0;
}

.city-select {
  flex: 1;
  min-width: 120px;
}

.city-select :deep(.ant-select-selector) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  color: white !important;
  font-size: 14px !important;
  padding: 0 8px !important;
}

.city-select :deep(.ant-select-selection-item) {
  color: white !important;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.city-select :deep(.ant-select-arrow) {
  color: rgba(255, 255, 255, 0.8) !important;
}

.city-select :deep(.ant-select-selection-placeholder) {
  color: rgba(255, 255, 255, 0.6) !important;
}



/* 加载和错误提示 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  color: #ff6b6b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  z-index: 10;
  text-align: center;
  padding: 0 20px;
}

/* 晴天样式 */
.sunny {
  background: linear-gradient(135deg, #FF9500, #FFDB5C);
}

.sunny-effect {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.sun {
  position: absolute;
  top: 25%;
  right: 25%;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #FFE87C;
  box-shadow: 0 0 40px rgba(255, 232, 124, 0.8);
  animation: sun-pulse 3s infinite ease-in-out;
}

.sun-rays {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 232, 124, 0) 0%, rgba(255, 232, 124, 0) 70%, rgba(255, 232, 124, 0.5) 71%, rgba(255, 232, 124, 0) 100%);
  background-size: 200% 200%;
  animation: rotate 20s infinite linear;
}

@keyframes sun-pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.9; }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 云的通用样式 */
.cloud {
  position: absolute;
  background: white;
  border-radius: 50%;
  filter: blur(3px);
  opacity: 0.9;
}

.cloud:before, .cloud:after {
  content: '';
  position: absolute;
  background: inherit;
  border-radius: inherit;
}

.large-cloud {
  width: 120px;
  height: 50px;
}

.large-cloud:before {
  width: 60px;
  height: 60px;
  top: -30px;
  left: 15px;
}

.large-cloud:after {
  width: 70px;
  height: 70px;
  top: -35px;
  right: 15px;
}

.medium-cloud {
  width: 90px;
  height: 40px;
}

.medium-cloud:before {
  width: 45px;
  height: 45px;
  top: -25px;
  left: 10px;
}

.medium-cloud:after {
  width: 55px;
  height: 55px;
  top: -30px;
  right: 10px;
}

.small-cloud {
  width: 60px;
  height: 30px;
}

.small-cloud:before {
  width: 30px;
  height: 30px;
  top: -15px;
  left: 10px;
}

.small-cloud:after {
  width: 35px;
  height: 35px;
  top: -18px;
  right: 10px;
}

.mini-cloud {
  width: 40px;
  height: 20px;
}

.mini-cloud:before {
  width: 20px;
  height: 20px;
  top: -10px;
  left: 7px;
}

.mini-cloud:after {
  width: 25px;
  height: 25px;
  top: -12px;
  right: 7px;
}

/* 雨天样式 */
.rainy {
  background: linear-gradient(135deg, #305F72, #14263D);
}

.rainy-effect {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.main-cloud {
  width: 180px;
  height: 60px;
  top: 25%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
  position: absolute;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

.main-cloud:before {
  content: '';
  position: absolute;
  width: 80px;
  height: 80px;
  top: -40px;
  left: 30px;
  background: inherit;
  border-radius: 50%;
}

.main-cloud:after {
  content: '';
  position: absolute;
  width: 100px;
  height: 100px;
  top: -50px;
  right: 30px;
  background: inherit;
  border-radius: 50%;
}

.side-cloud {
  width: 100px;
  height: 40px;
  background: rgba(255, 255, 255, 0.6);
  position: absolute;
}

.side-cloud:before {
  content: '';
  position: absolute;
  width: 50px;
  height: 50px;
  top: -25px;
  left: 15px;
  background: inherit;
  border-radius: 50%;
}

.side-cloud:after {
  content: '';
  position: absolute;
  width: 60px;
  height: 60px;
  top: -30px;
  right: 15px;
  background: inherit;
  border-radius: 50%;
}

.raindrop {
  position: absolute;
  top: -20px;
  width: 2px;
  height: 20px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.8));
  border-radius: 0 0 5px 5px;
  animation: rainFall linear infinite;
  filter: blur(0.5px);
  transform-origin: top;
  transform: rotate(15deg) translateY(0);
}

@keyframes rainFall {
  0% { transform: rotate(15deg) translateY(0); opacity: 0; }
  5% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: rotate(15deg) translateY(200px); opacity: 0; }
}

/* 多云样式 */
.cloudy {
  background: linear-gradient(135deg, #7B9EA8, #546F7A);
}

.cloudy-effect {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.cloud-1 {
  animation: cloud-move-1 25s linear infinite;
}

.cloud-2 {
  animation: cloud-move-2 20s linear infinite;
}

.cloud-3 {
  animation: cloud-move-3 30s linear infinite;
}

.cloud-4 {
  animation: cloud-move-4 18s linear infinite;
}

@keyframes cloud-move-1 {
  0% { transform: translateX(-100px); }
  100% { transform: translateX(400px); }
}

@keyframes cloud-move-2 {
  0% { transform: translateX(400px); }
  100% { transform: translateX(-100px); }
}

@keyframes cloud-move-3 {
  0% { transform: translateX(-50px); }
  100% { transform: translateX(350px); }
}

@keyframes cloud-move-4 {
  0% { transform: translateX(350px); }
  100% { transform: translateX(-50px); }
}

/* 雪天样式 */
.snowy {
  background: linear-gradient(135deg, #B8D8E3, #7DA3B4);
}

.snowy-effect {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.snowflake {
  position: absolute;
  top: -20px;
  width: 6px;
  height: 6px;
  animation: snowFall linear infinite;
  opacity: 0.8;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.8));
  transform-origin: center;
}

.snowflake svg {
  width: 100%;
  height: 100%;
  animation: snowRotate linear infinite;
  animation-duration: inherit;
}

@keyframes snowFall {
  0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
  10% { opacity: 1; }
  50% { transform: translateY(100px) translateX(20px) rotate(180deg); }
  90% { opacity: 0.8; }
  100% { transform: translateY(200px) translateX(-20px) rotate(360deg); opacity: 0; }
}

@keyframes snowRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 雷雨样式 */
.thunder {
  background: linear-gradient(135deg, #1F2F47, #0D1321);
}

.thunder-effect {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.thunder-clouds {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.thunder-cloud-1 {
  top: 20%;
  left: 30%;
  background: rgba(50, 50, 70, 0.9);
  animation: thunder-cloud-move 15s infinite ease-in-out;
}

.thunder-cloud-2 {
  top: 30%;
  left: 60%;
  background: rgba(60, 60, 80, 0.8);
  animation: thunder-cloud-move 18s 2s infinite ease-in-out;
}

@keyframes thunder-cloud-move {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(-20px); }
}

.lightning-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.lightning {
  position: absolute;
  background: rgba(255, 255, 180, 0.9);
  animation: lightning-flash 6s infinite;
  filter: blur(1px);
  box-shadow: 0 0 20px rgba(255, 255, 180, 0.8);
}

.lightning-1 {
  top: 30%;
  left: 40%;
  width: 3px;
  height: 80px;
  clip-path: polygon(
    50% 0%, 60% 40%, 100% 50%, 60% 60%, 90% 100%, 
    40% 80%, 0% 100%, 40% 60%, 0% 30%, 40% 40%
  );
}

.lightning-2 {
  top: 40%;
  left: 65%;
  width: 2px;
  height: 60px;
  animation-delay: 2s;
  clip-path: polygon(
    50% 0%, 90% 40%, 60% 50%, 100% 70%, 70% 100%, 
    40% 80%, 0% 100%, 30% 60%, 0% 40%, 40% 30%
  );
}

@keyframes lightning-flash {
  0%, 10%, 90%, 100% { opacity: 0; transform: scale(0.5); }
  7%, 8%, 9% { opacity: 1; transform: scale(1); }
  50%, 60%, 70% { opacity: 0; transform: scale(0.5); }
  57%, 58%, 59% { opacity: 1; transform: scale(1.2); }
}


</style>
