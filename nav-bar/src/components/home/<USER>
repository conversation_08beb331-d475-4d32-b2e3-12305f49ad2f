<template>
  <div class="time-section" :class="{ 'time-section-pure': isPureMode }">
    <div v-if="clockStyle === 'digital'" class="time" :class="clockColor" :style="dynamicTimeStyle">{{ timeString }}</div>
    <div v-else-if="clockStyle === 'analog'" class="analog" :class="clockColor"></div>
    <div class="current-date">{{ currentDate }}</div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted, watchEffect } from 'vue'
import lunisolar from 'lunisolar'



// 组件属性
const props = defineProps({
  isPureMode: {
    type: Boolean,
    default: false
  },
  // 外部样式配置（来自网格布局计算器）
  externalFontSize: {
    type: [String, Number],
    default: null
  },
  externalFontWeight: {
    type: [String, Number],
    default: null
  }
})

// 时间设置状态
const timeFormat = ref(localStorage.getItem('timeFormat') || '24h')
const dateFormat = ref(localStorage.getItem('dateFormat') || 'full')
const showSeconds = ref(localStorage.getItem('showSeconds') === 'true')
const showWeekday = ref(localStorage.getItem('showWeekday') !== 'false')
const clockStyle = ref(localStorage.getItem('clockStyle') || 'digital')
const clockColor = ref(localStorage.getItem('clockColor') || 'white')
const fontSize = ref(parseFloat(localStorage.getItem('fontSize')) || 4.3)
const fontWeight = ref(parseInt(localStorage.getItem('fontWeight')) || 700)
const fontFamily = ref(localStorage.getItem('fontFamily') || 'DIN')

// 字体选项映射
const fontFamilyMap = {
  'DIN': 'DIN',
  'Orbitron': "'Orbitron', monospace",
  'Roboto Mono': "'Roboto Mono', monospace",
  'Arial': "Arial, sans-serif",
  'Georgia': "Georgia, serif",
  'Helvetica': "Helvetica, Arial, sans-serif",
  'Fira Code': "'Fira Code', monospace"
}

// 时间日期管理
const currentTime = ref(new Date())
const timeString = computed(() => {
  const hours = currentTime.value.getHours()
  const minutes = currentTime.value.getMinutes().toString().padStart(2, '0')
  const seconds = currentTime.value.getSeconds().toString().padStart(2, '0')
  
  // 根据时间格式显示时间
  if (timeFormat.value === '12h') {
    const ampm = hours >= 12 ? 'PM' : 'AM'
    const hours12 = hours % 12 || 12
    return showSeconds.value ? `${hours12}:${minutes}:${seconds} ${ampm}` : `${hours12}:${minutes} ${ampm}`
  } else {
    // 24小时制
    const hours24 = hours.toString().padStart(2, '0')
    return showSeconds.value ? `${hours24}:${minutes}:${seconds}` : `${hours24}:${minutes}`
  }
})

const currentDate = computed(() => {
  return formatDateBySettings(currentTime.value)
})

// 动态字体样式（优先使用外部配置）
const dynamicTimeStyle = computed(() => {
  // 优先使用外部传入的字体配置
  let finalFontSize = fontSize.value
  let finalFontWeight = fontWeight.value

  // 如果有外部配置，则使用外部配置
  if (props.externalFontSize !== null) {
    // 外部配置可能是px单位的字符串，需要转换为rem
    if (typeof props.externalFontSize === 'string' && props.externalFontSize.includes('px')) {
      // 将px转换为rem（假设1rem = 16px）
      const pxValue = parseFloat(props.externalFontSize)
      finalFontSize = pxValue / 16
    } else {
      finalFontSize = parseFloat(props.externalFontSize) || fontSize.value
    }
  }

  if (props.externalFontWeight !== null) {
    finalFontWeight = props.externalFontWeight
  }

  return {
    fontSize: finalFontSize + 'rem',
    fontWeight: finalFontWeight,
    fontFamily: fontFamilyMap[fontFamily.value] || fontFamilyMap['Orbitron'],
    '--dynamic-font-size': finalFontSize + 'rem'
  }
})

const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']

// 根据设置格式化日期
function formatDateBySettings(date) {
  const month = date.getMonth() + 1
  const day = date.getDate()
  const weekDay = weekDays[date.getDay()]

  // 根据日期格式显示日期
  switch(dateFormat.value) {
    case 'short':
      const year = date.getFullYear()
      return `${year}/${month}/${day}${showWeekday.value ? ' ' + weekDay : ''}`
    case 'lunar': {
      // 使用更准确的农历计算
      const lunarDate = getLunarDate(date)
      return `农历${lunarDate.month}${lunarDate.day}${showWeekday.value ? ' ' + weekDay : ''}`
    }
    case 'full':
    default: {
      // 使用农历日期替换原来的季节显示
      const lunarDate = getLunarDate(date)
      return `${month}月${day}日${showWeekday.value ? ' ' + weekDay : ''} 农历${lunarDate.month}${lunarDate.day}`
    }
  }
}

// 获取农历日期
function getLunarDate(date) {
  // 使用 lunisolar 库进行农历转换
  const lsr = lunisolar(date)
  const lunar = lsr.lunar

  return {
    year: lunar.year,
    month: lunar.getMonthName(),
    day: lunar.getDayName(),
    isLeap: lunar.isLeapMonth
  }
}



// 更新时间
function updateTime() {
  currentTime.value = new Date()
}

// 监听时间设置变化事件
function handleTimeSettingsChanged(event) {
  if (event.detail) {


    // 更新组件状态
    timeFormat.value = event.detail.timeFormat || timeFormat.value
    dateFormat.value = event.detail.dateFormat || dateFormat.value
    showSeconds.value = event.detail.showSeconds !== undefined ? event.detail.showSeconds : showSeconds.value
    showWeekday.value = event.detail.showWeekday !== undefined ? event.detail.showWeekday : showWeekday.value
    clockStyle.value = event.detail.clockStyle || clockStyle.value
    clockColor.value = event.detail.clockColor || clockColor.value
    fontSize.value = event.detail.fontSize !== undefined ? event.detail.fontSize : fontSize.value
    fontWeight.value = event.detail.fontWeight !== undefined ? event.detail.fontWeight : fontWeight.value
    fontFamily.value = event.detail.fontFamily || fontFamily.value

    // 保存到本地存储，确保设置持久化
    localStorage.setItem('timeFormat', timeFormat.value)
    localStorage.setItem('dateFormat', dateFormat.value)
    localStorage.setItem('showSeconds', showSeconds.value.toString())
    localStorage.setItem('showWeekday', showWeekday.value.toString())
    localStorage.setItem('clockStyle', clockStyle.value)
    localStorage.setItem('clockColor', clockColor.value)
    localStorage.setItem('fontSize', fontSize.value.toString())
    localStorage.setItem('fontWeight', fontWeight.value.toString())
    localStorage.setItem('fontFamily', fontFamily.value)
    
    // 重新调整定时器间隔
    if (timer) clearInterval(timer)
    timer = setInterval(updateTime, showSeconds.value ? 1000 : 60000)
    
    // 立即更新一次时间显示
    updateTime()
    
    
  }
}

// 组件挂载时启动时间更新
let timer = null
onMounted(() => {
  // 立即更新一次时间
  updateTime()

  // 设置定时器，根据是否显示秒数决定更新频率
  timer = setInterval(updateTime, showSeconds.value ? 1000 : 60000)

  // 监听时间设置变化
  window.addEventListener('timeSettingsChanged', handleTimeSettingsChanged)
})

// 组件卸载时清除定时器和事件监听
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
  window.removeEventListener('timeSettingsChanged', handleTimeSettingsChanged)
})
</script>

<style scoped lang="scss">
.time-section {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  margin-top: 50px;
  user-select: none;
  
  .time {
    line-height: 1;
    display: block;
    margin: 0;
    padding: 0;
    font-family: 'DIN';
    font-weight: 700;
    font-style: Bold;
    // 响应式字体大小调整（基于动态设置的百分比）
    @media (max-width: 768px) {
      font-size: calc(var(--dynamic-font-size, 2.8rem) * 0.8) !important;
    }

    @media (max-width: 480px) {
      font-size: calc(var(--dynamic-font-size, 2.8rem) * 0.6) !important;
    }

    // 颜色类
    &.white { color: #ffffff; }
    &.blue { color: #3b82f6; }
    &.green { color: #10b981; }
    &.purple { color: #8b5cf6; }
    &.pink { color: #ec4899; }
    &.orange { color: #f97316; }
  }
  
  .current-date {
    font-size: 14px;
    color: #fff;
  }
}

.time-section-pure {
  margin-top: 60px;
  margin-bottom: 20px;

  .time {
    // 纯净模式下字体大小增加20%
    font-size: calc(var(--dynamic-font-size, 2.8rem)) !important;

    @media (max-width: 768px) {
      font-size: calc(var(--dynamic-font-size, 2.8rem) * 0.9) !important;
    }

    @media (max-width: 480px) {
      font-size: calc(var(--dynamic-font-size, 2.8rem) * 0.7) !important;
    }
  }
}

.analog {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 20px auto;
  border-radius: 50%;
  border: 3px solid currentColor;
  background: rgba(255, 255, 255, 0.1);
  
  &::before, &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: currentColor;
  }
  
  &::before {
    width: 3px;
    height: 40px;
    transform: translate(-50%, -100%);
  }
  
  &::after {
    width: 2px;
    height: 50px;
    transform: translate(-50%, -100%) rotate(90deg);
    transform-origin: bottom center;
  }
}
</style>
