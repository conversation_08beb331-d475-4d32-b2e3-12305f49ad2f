<template>
  <div class="link-card" :data-id="appId"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
    @mouseover="isHovered = true">
    <!-- 只在编辑模式或没有URL时显示标题栏 -->
    <div class="link-header" v-if="editMode || !storedUrl">
      <div class="link-title" :style="{ backgroundColor: headerColor || '#1E80FF' }">
        <span>{{ title || '迷你浏览器' }}</span>
        <div class="link-controls">
          
          <button class="control-btn maximize-btn" @click="toggleFullscreen" title="全屏">
            <img :src="full" class="right-icon"  />
          </button>
          <button class="control-btn external-btn" @click="openInNewWindow" title="在新窗口打开">
            <img :src="newWindow" class="right-icon"  />
          </button>
          <div class="link-edit-btn" @click="startEdit">
            <img :src="cgLink" class="right-icon"  />
          </div>
        </div>
      </div>
    </div>

    <!-- 加载URL后显示网站向前或者向后(非全屏模式且悬停时，或前景图关闭时) -->
    <div v-if="storedUrl && !editMode && (isHovered || !enableForeground)" class="floating-controls-left">
      <button class="control-btn back-btn"  :disabled="!canGoBack" title="后退">
        <img :src="gopre" class="right-icon" @click="goPrevious" />
      </button>
      <button class="control-btn forward-btn" :disabled="!canGoForward" title="前进">
        <img :src="gonext" class="right-icon" @click="goNext"/>
      </button>
    </div>
    
    <!-- 加载URL后显示浮动控制按钮(非全屏模式且悬停时，或前景图关闭时) -->
    <div v-if="storedUrl && !editMode && (isHovered || !enableForeground)" class="floating-controls">
      <button class="control-btn maximize-btn" @click="toggleFullscreen" title="全屏">
        <img :src="full" class="right-icon" />
      </button>
      <button class="control-btn external-btn" @click="openInNewWindow" title="在新窗口打开">
        <img :src="newWindow" class="right-icon" />
      </button>
      <div class="floating-edit-btn" @click="startEdit">
         <img :src="cgLink" class="right-icon" />
      </div>
    </div>
    
    <div class="link-body" :class="{'full-height': storedUrl && !editMode}">
      <!-- 前景图片 - 非悬停状态且启用前景图时显示 -->
      <img
        v-show="!isHovered && !editMode && enableForeground"
        :src="linkBg"
        class="link-front-image"
        alt="前景图片"
      />
      <template v-if="editMode">
        <div class="link-edit">
          <div class="preset-selector">
            <Select
              v-model:value="selectedPreset"
              :options="presetUrls"
              placeholder="选择预设网址或自定义输入"
              @change="handlePresetChange"
              class="preset-select"
              :dropdown-style="{ background: 'rgba(255, 255, 255, 0.95)', backdropFilter: 'blur(10px)' }"
            />
          </div>
          <input
            type="text"
            v-model="inputUrl"
            placeholder="请输入网页地址"
            class="link-input"
          />
          <div class="foreground-switch">
            <label class="switch-label">
              <Switch v-model:checked="enableForeground" size="small" />
              <span class="switch-text">启用前景图</span>
            </label>
            <div class="switch-description">开启后，鼠标悬停时才显示网页内容</div>
          </div>
          <div class="link-buttons">
            <button class="link-button save" @click="saveLink">加载</button>
            <button class="link-button cancel" @click="cancelEdit">取消</button>
          </div>
        </div>
      </template>
      <!-- 空状态显示 -->
      <div v-show="!storedUrl && !editMode && (isHovered || !enableForeground)" class="link-empty" @click="startEdit">
        <div class="link-icon">
          <div style="font-size: 24px;">🔗</div>
        </div>
        <div class="link-url">点击添加网页链接</div>
      </div>

      <!-- iframe容器 - 始终存在于DOM中，只控制可见性 -->
      <div
        v-if="storedUrl"
        class="iframe-container"
        :style="{ display: (!editMode && (isHovered || !enableForeground)) ? 'block' : 'none' }"
        @click="handleIframeInteraction"
        @mousedown="handleIframeInteraction"
        @touchstart="handleIframeInteraction">
        <iframe
          :src="storedUrl"
          frameborder="0"
          class="embedded-iframe"
          allowfullscreen
          loading="lazy"
          referrerpolicy="no-referrer"
          ></iframe>
        <div class="iframe-loading" v-if="isLoading">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载中...</div>
          <button class="manual-complete-btn" @click.stop="manualCompleteLoading">
            内容已加载完成(如已经加载)
          </button>
        </div>
        <div class="iframe-error" v-if="loadError">
          <div class="error-icon">⚠️</div>
          <div class="error-message">内容加载超时或失败</div>
          <div class="error-description">如果内容已经可用，请点击"内容已加载"</div>
          <div class="error-buttons">
            <button class="retry-button" @click.stop="reloadIframe">重新加载</button>
            <button class="manual-complete-btn" @click.stop="manualCompleteLoading">内容已加载</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 使用Teleport将全屏模态框传送到body -->
    <Teleport to="body">
      <div class="app-modal-overlay" v-if="isFullscreen" @click.self="handleOverlayClick">
        <div class="app-modal app-modal-fullscreen">
          <div class="app-modal-header">
            <div class="app-modal-spacer"></div>
            <div class="app-modal-title">{{ title || '迷你浏览器' }}</div>
            <div class="app-modal-controls">
              <button class="control-btn maximize-btn" @click="toggleFullscreen" title="退出全屏">
                <img :src="noFull" class="full-right" />
              </button>
              <button class="control-btn maximize-btn" @click="openInNewWindow" title="在新窗口打开">
                <img :src="newWindowsFull" alt="fullscreen" class="full-right"  />
              </button>
              <!-- <button class="control-btn close-btn" @click="toggleFullscreen" title="关闭">
                <i class="icon icon-close"></i>
              </button> -->
              <button class="control-btn close-btn" @click="handleCloseButtonClick" title="关闭">
                <img :src="closeSvg" alt="close" />
              </button>
            </div>
          </div>
          <div class="app-modal-content">
            <iframe 
              v-if="storedUrl" 
              :src="storedUrl" 
              frameborder="0" 
              class="app-iframe"
              allowfullscreen
              loading="lazy"
              referrerpolicy="no-referrer"
              ></iframe>
            <div v-else class="empty-content">
              <i class="icon icon-warning"></i>
              <p>无效的URL</p>
            </div>
            <div class="iframe-loading" v-if="isLoading">
              <div class="loading-spinner"></div>
              <div class="loading-text">加载中...</div>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, onMounted, watch, onUnmounted, computed } from 'vue';
import { Modal, Select, Switch } from 'ant-design-vue';
import { getLinkList } from '@/api/imageCard.js';
import { useSettingStore } from '@/stores/setting.js';
import linkBg from '@/assets/linkCard/linkBg.png';
import  closeSvg  from '@/assets/modal/close.svg'
import  fullscreen  from '@/assets/modal/fullscreen.svg'
import autoBackupManager from '@/utils/autoBackupManager'
import full from '@/assets/icons/full.svg'
import newWindow from '@/assets/icons/newWindow.svg'
import cgLink from '@/assets/icons/cgLink.svg'

import noFull from '@/assets/icons/noFull.svg'
import newWindowsFull from '@/assets/icons/newWindowsFull.svg'

import gopre from '@/assets/icons/gopre.svg'
import gonext from '@/assets/icons/gonext.svg'
const settingStore = useSettingStore();

const props = defineProps({
  url: {
    type: String,
    default: ''
  },
  appId: {
    type: [Number, String],
    required: true
  },
  title: {
    type: String,
    default: '迷你浏览器'
  },
  headerColor: {
    type: String,
    default: '#1E80FF'
  }
});

const emit = defineEmits(['update:url']);

const editMode = ref(false);
const inputUrl = ref(props.url || '');
const storedUrl = ref(props.url || '');
const isLoading = ref(false);
const loadError = ref(false);
const isFullscreen = ref(false);
// 新增：鼠标悬停状态
const isHovered = ref(false);
// 新增：前景图开关状态，默认关闭
const enableForeground = ref(false);

// 历史记录管理
const urlHistory = ref([]);
const currentHistoryIndex = ref(-1);

// 计算属性：是否可以前进后退
const canGoBack = computed(() => currentHistoryIndex.value > 0);
const canGoForward = computed(() => currentHistoryIndex.value < urlHistory.value.length - 1);

// 预设网址选项
const presetUrls = ref();

// 当前选中的预设选项
const selectedPreset = ref('');

// 默认URL（Office API）
const DEFAULT_URL = props.url || '';

// 根据URL查找对应的预设选项
function findPresetByUrl(url) {
  if (!url) return '';

  // 查找匹配的预设选项
  const matchedPreset = presetUrls.value.find(preset =>
    preset.value !== 'custom' && preset.value === url
  );

  return matchedPreset ? matchedPreset.value : '';
}

// 处理预设网址选择变更
function handlePresetChange(value) {
  if (value === 'custom') {
    // 选择自定义输入，清空输入框并聚焦
    inputUrl.value = '';
  } else {
    // 选择预设网址，自动填充到输入框
    inputUrl.value = value;
  }
}

// 开始编辑模式
function startEdit(e) {
  if (e) e.stopPropagation();
  editMode.value = true;
  inputUrl.value = storedUrl.value;
  // 根据当前URL匹配对应的预设选项
  selectedPreset.value = findPresetByUrl(storedUrl.value);
  // 注意：enableForeground.value 已经在加载时设置，这里不需要重新设置
}

// 保存链接
function saveLink() {
  if (!inputUrl.value) {
    Modal.warn({
      title: '提示',
      centered: true,
      content: '请输入有效的网页地址',
    });
    return;
  }
  
  // 确保链接有http前缀
  let processedUrl = inputUrl.value.trim();
  if (!processedUrl.startsWith('http://') && !processedUrl.startsWith('https://')) {
    processedUrl = 'https://' + processedUrl;
  }
  
  storedUrl.value = processedUrl;
  editMode.value = false;
  isLoading.value = true;
  loadError.value = false;

  // 添加到历史记录
  addToHistory(processedUrl);

  // 通知父组件URL已更新
  emit('update:url', processedUrl);

  // 存储到本地存储中（包含URL和前景图设置）
  const storageKey = `link-card-${props.appId}`;
  const config = {
    url: processedUrl,
    enableForeground: enableForeground.value
  };
  localStorage.setItem(storageKey, JSON.stringify(config));
  autoBackupManager.triggerAutoBackup();
}

console.log(props.appId,'props.appId')

// 取消编辑
function cancelEdit() {
  editMode.value = false;
  inputUrl.value = storedUrl.value;
}

const handleOverlayClick = () => {
  if (settingStore.closeModalOnOutsideClick) {
    toggleFullscreen();
  }
};

const handleCloseButtonClick = () => {
  if (settingStore.closeModalOnButtonClick) {
    toggleFullscreen();
  }
};

// 切换全屏状态
function toggleFullscreen(e) {
  if (e) e.stopPropagation();
  
  isFullscreen.value = !isFullscreen.value;
  
  // 如果是全屏，添加body类禁用滚动
  if (isFullscreen.value) {
    document.body.classList.add('link-card-fullscreen-active');
  } else {
    document.body.classList.remove('link-card-fullscreen-active');
  }
}

// 在新窗口打开
function openInNewWindow(e) {
  if (e) e.stopPropagation();

  if (storedUrl.value) {
    window.open(storedUrl.value, '_blank');
  } else {
    Modal.warn({
      title: '提示',
      centered: true,
      content: '请先添加网页链接',
    });
  }
}

// 后退功能
function goPrevious(e) {
  history.back();
}
// 前进功能
function goNext(e) {
  history.forward();
}

// 添加到历史记录
function addToHistory(url) {
  if (!url) return;

  // 如果当前不在历史记录的末尾，删除当前位置之后的所有记录
  if (currentHistoryIndex.value < urlHistory.value.length - 1) {
    urlHistory.value = urlHistory.value.slice(0, currentHistoryIndex.value + 1);
  }

  // 如果新URL与当前URL不同，添加到历史记录
  if (urlHistory.value[currentHistoryIndex.value] !== url) {
    urlHistory.value.push(url);
    currentHistoryIndex.value = urlHistory.value.length - 1;
  }
}

// 重新加载iframe
function reloadIframe() {
  if (!storedUrl.value) return;

  isLoading.value = true;
  loadError.value = false;

  // 通过改变URL来强制刷新iframe
  const tempUrl = storedUrl.value;
  storedUrl.value = '';

  setTimeout(() => {
    storedUrl.value = tempUrl;
  }, 100);
}

// 处理用户与iframe的交互
function handleIframeInteraction() {
  // 如果正在加载且用户开始交互，说明内容已经可用
  if (isLoading.value) {
    isLoading.value = false;
    loadError.value = false;
  }
}

// 手动完成加载
function manualCompleteLoading() {
  console.log('手动完成加载被调用');
  isLoading.value = false;
  loadError.value = false;
  console.log('加载状态已更新:', { isLoading: isLoading.value, loadError: loadError.value });
}

// 检测iframe加载状态
function setupIframeListeners() {
  setTimeout(() => {
    const iframe = document.querySelector(`.link-card[data-id="${props.appId}"] iframe`);
    if (iframe) {
      // 主要的加载完成事件
      iframe.onload = () => {
        // 延迟一点时间确保内容真正可用
        setTimeout(() => {
          isLoading.value = false;
          loadError.value = false;
        }, 1000);
      };

      // 加载错误事件
      iframe.onerror = () => {
        isLoading.value = false;
        loadError.value = true;
      };

      // 添加额外的事件监听，适应不同类型的内容
      iframe.addEventListener('load', () => {
        setTimeout(() => {
          isLoading.value = false;
          loadError.value = false;
        }, 1000);
      });
    }
  }, 100);
}

// 当storedUrl变化时，重新设置iframe监听器
watch(storedUrl, () => {
  if (storedUrl.value) {
    isLoading.value = true;
    loadError.value = false;
    setupIframeListeners();
  }
});

// 监听键盘事件，在全屏状态下只允许ESC键退出全屏
function handleKeyDown(e) {
  // 只在全屏状态下处理键盘事件
  if (isFullscreen.value) {
    if (e.key === 'Escape') {
      // 只有ESC键可以退出全屏
      toggleFullscreen();
    } else {
      // 阻止所有其他按键的默认行为，防止意外操作
      e.preventDefault();
      e.stopPropagation();
    }
  }
}

function getCurrentLInkList(){
  if(props.appId.indexOf('LinkCard') !== -1) return
  const id = Number(props.appId)
  getLinkList(id).then(res => {
    if(res.status == 200) {
      presetUrls.value = res.data.map(item => ({
        label: item.name,
        value: item.url
      }))
    }
  })
}

// 组件挂载
onMounted(() => {
  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown);
  getCurrentLInkList()
  // 尝试从本地存储加载保存的配置
  const storageKey = `link-card-${props.appId}`;
  const savedData = localStorage.getItem(storageKey);

  if (savedData) {
    try {
      // 尝试解析为JSON对象（新格式）
      const config = JSON.parse(savedData);
      if (config && typeof config === 'object' && config.url) {
        storedUrl.value = config.url;
        enableForeground.value = config.enableForeground || false;
        // 初始化历史记录
        addToHistory(config.url);
      } else {
        throw new Error('Invalid config format');
      }
    } catch (e) {
      // 如果解析失败，说明是旧格式（字符串URL）
      storedUrl.value = savedData;
      enableForeground.value = false; // 旧数据默认不启用前景图
      // 初始化历史记录
      addToHistory(savedData);
      // 更新为新格式
      const config = {
        url: savedData,
        enableForeground: false
      };
      localStorage.setItem(storageKey, JSON.stringify(config));
    }
    isLoading.value = true;
    setupIframeListeners();
  } else if (props.url) {
    // 如果没有保存的但有props url，则使用props
    storedUrl.value = props.url;
    enableForeground.value = false; // 默认不启用前景图
    // 初始化历史记录
    addToHistory(props.url);
    isLoading.value = true;
    setupIframeListeners();
    // 保存到本地存储
    const config = {
      url: props.url,
      enableForeground: false
    };
    localStorage.setItem(storageKey, JSON.stringify(config));
  } else {
    // 如果没有任何URL，使用默认的Office API
    storedUrl.value = DEFAULT_URL;
    enableForeground.value = false; // 默认不启用前景图
    // 初始化历史记录
    addToHistory(DEFAULT_URL);
    isLoading.value = true;
    setupIframeListeners();
    // 保存默认配置到本地存储
    const config = {
      url: DEFAULT_URL,
      enableForeground: false
    };
    localStorage.setItem(storageKey, JSON.stringify(config));
  }
  
  // 设置定时器检查iframe加载状态
  setTimeout(() => {
    if (isLoading.value) {
      loadError.value = true;
      isLoading.value = false;
    }
  }, 20000); // 20秒后如果还在加载，认为加载失败（适应视频和复杂内容）
});

// 组件卸载时清理
onUnmounted(() => {
  // 移除键盘事件监听
  window.removeEventListener('keydown', handleKeyDown);
  
  // 确保退出全屏
  if (isFullscreen.value) {
    document.body.classList.remove('link-card-fullscreen-active');
  }
});
</script>

<style scoped lang="scss">
.full-right{
  width: 20px;
  height: 20px;
}

.link-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f7f7;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
}

.link-header {
  width: 100%;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.link-title {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  font-size: 14px;
  font-weight: 500;
  padding: 0 12px;
}

.right-icon{
  width: 24px;
  height: 24px;
}

.link-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 标题栏中控制按钮的特殊样式 */
.link-controls .control-btn {
  width: 20px;
  height: 20px;
}

.link-controls .control-btn .icon,
.link-controls .control-btn svg {
  width: 10px;
  height: 10px;
}

.control-btn {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  // box-shadow: 0 0 0 0.5px rgba(0, 0, 0, 0.2) inset;
  padding: 0;
  box-sizing: border-box;
  outline: none;
  border: none;
  background: none;
}

.control-btn .icon {
  width: 8px;
  height: 8px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.control-btn svg {
  width: 8px;
  height: 8px;
}

// 禁用状态的按钮样式
.control-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;

  &:hover {
    opacity: 0.4;
  }
}



.link-edit-btn {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }
}

/* 前景图片样式 */
.link-front-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.15s ease;
}

.floating-controls-left {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  gap: 8px;
  z-index: 10;
  opacity: 1;
  transition: opacity 0.3s;
  align-items: center;
}

.floating-controls-left .control-btn {
  width: 24px;
  height: 24px;
}

/* 浮动控制按钮区域 */
.floating-controls {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  z-index: 10;
  opacity: 1;
  transition: opacity 0.3s;
  align-items: center;
}

/* 浮动控制按钮的特殊样式 */
.floating-controls .control-btn {
  width: 24px;
  height: 24px;
}

.floating-controls .control-btn .icon,
.floating-controls .control-btn svg {
  width: 12px;
  height: 12px;
}




/* 浮动编辑按钮 - 当有URL且不在编辑模式时显示 */
.floating-edit-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  // background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: white;
  transition: background-color 0.3s;
}

.link-body {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

/* 当没有标题栏时占满整个高度 */
.full-height {
  width: 100%;
  height: 100%;
}

.link-edit {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 15px;
  gap: 10px;
  background-color: #f7f7f7;
}

.preset-selector {
  width: 100%;
  margin-bottom: 8px;
}

.preset-select {
  width: 100%;
}

.preset-select :deep(.ant-select-selector) {
  border-radius: 8px;
  border: 1px solid #ddd;
  background-color: white;
  transition: border-color 0.3s;
}

.preset-select :deep(.ant-select-selector:hover) {
  border-color: var(--accent-color, #1E80FF);
}

.preset-select :deep(.ant-select-focused .ant-select-selector) {
  border-color: var(--accent-color, #1E80FF);
  box-shadow: 0 0 0 2px rgba(30, 128, 255, 0.1);
}

.link-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s;
  background-color: white;
  color: #333;

  &:focus {
    border-color: var(--accent-color, #1E80FF);
    box-shadow: 0 0 0 2px rgba(30, 128, 255, 0.1);
  }
}

.foreground-switch {
  margin: 10px 0;
  padding: 8px 0;
}

.switch-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  margin-bottom: 4px;
}

.switch-text {
  font-size: 14px;
  color: #333;
  user-select: none;
}

.switch-description {
  font-size: 12px;
  color: #666;
  margin-left: 24px;
  line-height: 1.4;
}

.link-buttons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.link-button {
  flex: 1;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.3s;
  
  &.save {
    background-color: var(--accent-color, #1E80FF);
    color: white;
  }
  
  &.cancel {
    background-color: #f0f0f0;
    color: #666;
    
    &:hover {
      background-color: #e0e0e0;
    }
  }
}

.link-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.9);
  }
}

.link-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(30, 128, 255, 0.1);
  color: var(--accent-color, #1E80FF);
  margin-bottom: 5px;
}

.link-url {
  font-size: 14px;
  color: #333;
  text-align: center;
}

.iframe-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.embedded-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background-color: white;
}

.iframe-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(30, 128, 255, 0.2);
  border-top-color: rgba(30, 128, 255, 1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #333;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.iframe-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
}

.error-icon {
  font-size: 28px;
  margin-bottom: 10px;
}

.error-message {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  text-align: center;
}

.error-description {
  font-size: 12px;
  color: #888;
  margin-bottom: 15px;
  text-align: center;
}

.error-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.retry-button {
  padding: 6px 15px;
  background-color: var(--accent-color, #1E80FF);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.3s;
}

.manual-complete-btn {
  padding: 6px 15px;
  background-color: #eeeeee;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.3s;
  margin-top: 10px;
}

.manual-complete-btn:hover {
  background-color: #218838;
}

/* 图标样式 */
.icon {
  display: inline-block;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-maximize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='5' y='5' width='14' height='14' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='8' y1='5' x2='8' y2='3'%3E%3C/line%3E%3Cline x1='16' y1='5' x2='16' y2='3'%3E%3C/line%3E%3Cline x1='5' y1='8' x2='3' y2='8'%3E%3C/line%3E%3Cline x1='5' y1='16' x2='3' y2='16'%3E%3C/line%3E%3C/svg%3E");
}

.icon-minimize {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
}

.icon-close {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
}

.icon-warning {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z'%3E%3C/path%3E%3Cline x1='12' y1='9' x2='12' y2='13'%3E%3C/line%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'%3E%3C/line%3E%3C/svg%3E");
  width: 48px;
  height: 48px;
}

/* AppModal样式 */
.app-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.app-modal {
  width: 90%;
  height: 80%;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  max-width: 1200px;
  position: relative;
  z-index: 1001;
}

.app-modal-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw !important;
  height: 100vh !important;
  max-width: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0;
  z-index: 10000;
}

.app-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f7;
  border-bottom: 1px solid #eaeaea;
  height: 53px;
  -webkit-app-region: drag;
}

.app-modal-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  flex: 1;
  user-select: none;
}

.app-modal-spacer {
  width: 60px;
}

.app-modal-controls {
  display: flex;
  gap: 10px;
  margin-left: 4px;
  -webkit-app-region: no-drag;
}

.app-modal-controls .control-btn {
  width: 16px;
  height: 16px;
  padding: 0;
}

.app-modal-controls .control-btn .icon,
.app-modal-controls .control-btn svg {
  width: 6px;
  height: 6px;
}

.app-modal-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: #fff;
}

.app-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

.empty-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  gap: 12px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 响应式适配 */
@media (max-width: 768px) {
  .link-title {
    font-size: 13px;
  }
  
  .link-input {
    padding: 6px 10px;
    font-size: 13px;
  }
  
  .link-button {
    padding: 5px 10px;
    font-size: 12px;
  }
  
  .link-icon {
    width: 50px;
    height: 50px;
  }
  
  .link-url {
    font-size: 13px;
  }
}
</style>

<style>
/* 全局样式，确保全屏模式下禁用页面滚动 */
body.link-card-fullscreen-active {
  overflow: hidden !important;
  padding: 0 !important;
  margin: 0 !important;
}
</style>
